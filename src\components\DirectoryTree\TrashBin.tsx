import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  TrashIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  RestoreIcon,
  DeleteIcon,
  DocumentIcon,
  FolderIcon,
  HtmlIcon
} from '../Icons/IconLibrary'
import { useDirectoryStore } from '../../stores/directoryStore'
import { DirectoryItem } from '../../services/directoryService'
import TrashManagementModal from './TrashManagementModal'
import { Settings } from 'lucide-react'

interface TrashBinProps {
  className?: string
}

const TrashBin: React.FC<TrashBinProps> = ({ className = '' }) => {
  const {
    trashItems,
    loading,
    restoreItem,
    permanentDeleteItem,
    emptyTrash
  } = useDirectoryStore()

  const [isExpanded, setIsExpanded] = useState(false)
  const [showEmptyConfirm, setShowEmptyConfirm] = useState(false)
  const [showManagementModal, setShowManagementModal] = useState(false)

  // 格式化删除时间
  const formatDeletedTime = (deletedAt: string) => {
    const date = new Date(deletedAt)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  // 获取文件图标
  const getFileIcon = (item: DirectoryItem) => {
    if (item.type === 'folder') {
      return FolderIcon
    } else if (item.type === 'html') {
      return HtmlIcon
    } else {
      return DocumentIcon
    }
  }

  // 处理恢复
  const handleRestore = async (id: string, name: string) => {
    if (window.confirm(`确定要恢复 "${name}" 吗？`)) {
      try {
        await restoreItem(id)
      } catch (error) {
        console.error('Failed to restore item:', error)
      }
    }
  }

  // 处理永久删除
  const handlePermanentDelete = async (id: string, name: string) => {
    if (window.confirm(`确定要永久删除 "${name}" 吗？此操作不可撤销！`)) {
      try {
        await permanentDeleteItem(id)
      } catch (error) {
        console.error('Failed to permanently delete item:', error)
      }
    }
  }

  // 处理清空回收站
  const handleEmptyTrash = async () => {
    if (window.confirm('确定要清空回收站吗？此操作将永久删除所有项目，不可撤销！')) {
      try {
        await emptyTrash()
        setShowEmptyConfirm(false)
      } catch (error) {
        console.error('Failed to empty trash:', error)
      }
    }
  }

  return (
    <div className={`${className}`}>
      {/* 回收站标题 */}
      <div
        className={`
          flex items-center justify-between px-3 py-2 rounded-md cursor-pointer
          transition-all duration-200 ease-out
          hover:bg-theme-surface-hover
          ${isExpanded ? 'bg-theme-surface-hover' : ''}
        `}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {/* 展开/折叠图标 */}
          <div className="w-4 h-4 flex items-center justify-center">
            {isExpanded ? (
              <ChevronDownIcon className="icon-xs text-theme-text-muted" />
            ) : (
              <ChevronRightIcon className="icon-xs text-theme-text-muted" />
            )}
          </div>
          
          {/* 回收站图标 */}
          <TrashIcon className="icon-sm text-theme-text-muted" />
          
          {/* 标题和数量 */}
          <span className="text-sm text-theme-text">
            回收站 ({trashItems.length})
          </span>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1">
          {/* 管理回收站按钮 */}
          <button
            className="p-1 text-theme-text-muted hover:text-theme-primary transition-colors"
            onClick={(e) => {
              e.stopPropagation()
              setShowManagementModal(true)
            }}
            title="管理回收站"
          >
            <Settings className="w-3 h-3" />
          </button>

          {/* 清空按钮 */}
          {trashItems.length > 0 && (
            <button
              className="p-1 text-theme-text-muted hover:text-theme-danger transition-colors"
              onClick={(e) => {
                e.stopPropagation()
                setShowEmptyConfirm(true)
              }}
              title="清空回收站"
            >
              <DeleteIcon className="icon-xs" />
            </button>
          )}
        </div>
      </div>

      {/* 回收站内容 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="ml-6 mt-1 space-y-1">
              {trashItems.length === 0 ? (
                <div className="px-3 py-2 text-sm text-theme-text-muted">
                  回收站为空
                </div>
              ) : (
                trashItems.map((item) => {
                  const Icon = getFileIcon(item)
                  
                  return (
                    <motion.div
                      key={item._id}
                      className={`
                        flex items-center gap-2 px-3 py-2 rounded-md
                        transition-all duration-200 ease-out
                        hover:bg-theme-surface-hover group
                      `}
                      whileHover={{ x: 2 }}
                    >
                      {/* 文件图标 */}
                      <Icon className={`
                        icon-sm flex-shrink-0
                        ${item.type === 'folder' 
                          ? 'text-theme-folder opacity-60' 
                          : item.type === 'html' 
                            ? 'text-theme-html opacity-60' 
                            : 'text-theme-markdown opacity-60'
                        }
                      `} />

                      {/* 文件信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="text-sm text-theme-text-muted truncate">
                          {item.name}
                        </div>
                        <div className="text-xs text-theme-text-muted">
                          {item.deletedAt && formatDeletedTime(item.deletedAt)}
                          {item.path && ` • ${item.path}`}
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center opacity-0 group-hover:opacity-100">
                        <button
                          className="p-1 text-theme-text-muted hover:text-theme-success"
                          onClick={() => handleRestore(item._id, item.name)}
                          title="恢复"
                        >
                          <RestoreIcon className="icon-xs" />
                        </button>
                        <button
                          className="p-1 text-theme-text-muted hover:text-theme-danger"
                          onClick={() => handlePermanentDelete(item._id, item.name)}
                          title="永久删除"
                        >
                          <DeleteIcon className="icon-xs" />
                        </button>
                      </div>
                    </motion.div>
                  )
                })
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 清空确认对话框 */}
      {showEmptyConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-theme-bg border border-theme-border rounded-lg p-6 max-w-md mx-4"
          >
            <h3 className="text-lg font-semibold text-theme-text mb-4">
              清空回收站
            </h3>
            <p className="text-theme-text-muted mb-6">
              确定要清空回收站吗？这将永久删除所有 {trashItems.length} 个项目，此操作不可撤销！
            </p>
            <div className="flex gap-3 justify-end">
              <button
                className="px-4 py-2 text-theme-text-muted hover:text-theme-text"
                onClick={() => setShowEmptyConfirm(false)}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-theme-danger text-white rounded-md hover:bg-theme-danger/80"
                onClick={handleEmptyTrash}
                disabled={loading}
              >
                {loading ? '删除中...' : '确认删除'}
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* 回收站管理弹窗 */}
      <TrashManagementModal
        isOpen={showManagementModal}
        onClose={() => setShowManagementModal(false)}
      />
    </div>
  )
}

export default TrashBin
