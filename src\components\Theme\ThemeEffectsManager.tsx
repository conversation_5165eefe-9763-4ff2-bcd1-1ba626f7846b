import React, { useEffect, useState } from 'react'
import { useThemeStore } from '../../stores/themeStore'

// 简化的太阳系效果组件
const SolarEffects: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  if (!isActive) return null

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {/* 简化的太阳光晕效果 */}
      <div className="absolute top-8 right-8 w-12 h-12 rounded-full bg-gradient-radial from-theme-primary/30 via-theme-primary/10 to-transparent animate-pulse opacity-40" />

      {/* 简化的星空背景 */}
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-theme-void/20 to-theme-background" />
    </div>
  )
}

// 星云效果组件已移除

// 主题切换过渡效果
const ThemeTransition: React.FC<{ isTransitioning: boolean }> = ({ isTransitioning }) => {
  if (!isTransitioning) return null

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      <div className="absolute inset-0 bg-gradient-radial from-theme-primary/20 via-transparent to-theme-secondary/20 animate-pulse" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-16 h-16 border-4 border-theme-primary/30 border-t-theme-primary rounded-full animate-spin" />
      </div>
    </div>
  )
}

// 主题效果管理器
const ThemeEffectsManager: React.FC = () => {
  const { isTransitioning } = useThemeStore()
  const [effectsEnabled, setEffectsEnabled] = useState(true)

  // 监听用户偏好设置
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setEffectsEnabled(!mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setEffectsEnabled(!e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // 监听主题切换事件
  useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      console.log('主题切换到:', event.detail.theme)
    }

    window.addEventListener('themeChange', handleThemeChange as EventListener)
    return () => window.removeEventListener('themeChange', handleThemeChange as EventListener)
  }, [])

  if (!effectsEnabled) return null

  return (
    <>
      {/* 太阳系主题效果 */}
      <SolarEffects isActive={true} />

      {/* 主题切换过渡效果 */}
      <ThemeTransition isTransitioning={isTransitioning} />

      {/* 星空背景 */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-theme-void/50 to-theme-background" />
        {Array.from({ length: 100 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px h-px bg-theme-text-secondary rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
    </>
  )
}

export default ThemeEffectsManager
