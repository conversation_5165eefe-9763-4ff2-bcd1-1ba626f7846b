/* 云笔记应用 - 双主题系统 */

/* 深色主题（默认） - 优化版本 */
:root {
  /* 基础色彩 - 增强层次感 */
  --theme-bg: #0d1117;
  --theme-bg-secondary: #161b22;
  --theme-bg-tertiary: #1c2128;
  --theme-surface: #21262d;
  --theme-surface-hover: #30363d;

  /* 边框色彩 - 提升可见性 */
  --theme-border: #30363d;
  --theme-border-muted: #21262d;
  --theme-border-subtle: #484f58;
  --theme-border-focus: #58a6ff;

  /* 文本色彩 - 优化对比度 */
  --theme-text: #f0f6fc;
  --theme-text-muted: #9198a1;
  --theme-text-subtle: #7d8590;
  
  /* 主色调 */
  --theme-primary: #58a6ff;
  --theme-primary-hover: #79c0ff;
  --theme-primary-muted: #1f6feb;
  
  /* 次要色调 */
  --theme-secondary: #f85149;
  --theme-secondary-hover: #ff7b72;
  --theme-secondary-muted: #da3633;
  
  /* 成功色 */
  --theme-success: #3fb950;
  --theme-success-hover: #56d364;
  --theme-success-muted: #238636;
  
  /* 警告色 */
  --theme-warning: #d29922;
  --theme-warning-hover: #e3b341;
  --theme-warning-muted: #9e6a03;
  
  /* 危险色 */
  --theme-danger: #f85149;
  --theme-danger-hover: #ff7b72;
  --theme-danger-muted: #da3633;
  
  /* 阴影 - 深色主题优化 */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4);
  --theme-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.5), 0 1px 2px 0 rgba(0, 0, 0, 0.4);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);

  /* 交互状态 */
  --theme-focus-ring: 0 0 0 2px var(--theme-primary);
  --theme-focus-ring-offset: 0 0 0 2px var(--theme-surface);
  --theme-transition-fast: 0.15s ease-out;
  --theme-transition-normal: 0.2s ease-out;
  
  /* 特殊效果 */
  --theme-backdrop: rgba(13, 17, 23, 0.8);
  --theme-overlay: rgba(48, 54, 61, 0.9);
  
  /* 编辑器相关 */
  --theme-editor-bg: #0d1117;
  --theme-editor-selection: #264f78;
  --theme-editor-line-highlight: #2f363d;
  
  /* 搜索高亮 */
  --theme-search-highlight-bg: #ffd33d44;
  --theme-search-highlight-text: #ffd33d;

  /* 字体和排版优化 */
  --theme-font-weight-normal: 500;
  --theme-font-weight-medium: 600;
  --theme-font-weight-bold: 700;
  --theme-line-height-normal: 1.6;
  --theme-line-height-tight: 1.4;
  --theme-letter-spacing: 0.01em;
}

/* 浅色主题 */
[data-theme="light"] {
  /* 基础色彩 */
  --theme-bg: #ffffff;
  --theme-bg-secondary: #f6f8fa;
  --theme-bg-tertiary: #f1f3f4;
  --theme-surface: #ffffff;
  --theme-surface-hover: #f6f8fa;
  
  /* 边框色彩 */
  --theme-border: #d0d7de;
  --theme-border-muted: #d8dee4;
  --theme-border-subtle: #afb8c1;
  
  /* 文本色彩 */
  --theme-text: #24292f;
  --theme-text-muted: #656d76;
  --theme-text-subtle: #8c959f;
  
  /* 主色调 */
  --theme-primary: #0969da;
  --theme-primary-hover: #0550ae;
  --theme-primary-muted: #218bff;
  
  /* 次要色调 */
  --theme-secondary: #cf222e;
  --theme-secondary-hover: #a40e26;
  --theme-secondary-muted: #ff6b80;
  
  /* 成功色 */
  --theme-success: #1a7f37;
  --theme-success-hover: #116329;
  --theme-success-muted: #2da44e;
  
  /* 警告色 */
  --theme-warning: #9a6700;
  --theme-warning-hover: #7d4e00;
  --theme-warning-muted: #bf8700;
  
  /* 危险色 */
  --theme-danger: #cf222e;
  --theme-danger-hover: #a40e26;
  --theme-danger-muted: #ff6b80;
  
  /* 阴影 - 浅色主题 */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --theme-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.05);

  /* 交互状态 */
  --theme-focus-ring: 0 0 0 2px var(--theme-primary);
  --theme-focus-ring-offset: 0 0 0 2px var(--theme-bg);
  --theme-transition-fast: 0.15s ease-out;
  --theme-transition-normal: 0.2s ease-out;
  
  /* 特殊效果 */
  --theme-backdrop: rgba(255, 255, 255, 0.8);
  --theme-overlay: rgba(255, 255, 255, 0.9);
  
  /* 编辑器相关 */
  --theme-editor-bg: #ffffff;
  --theme-editor-selection: #add6ff;
  --theme-editor-line-highlight: #f6f8fa;
  
  /* 搜索高亮 */
  --theme-search-highlight-bg: #fff8c5;
  --theme-search-highlight-text: #9a6700;

  /* 字体和排版优化 */
  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-bold: 600;
  --theme-line-height-normal: 1.5;
  --theme-line-height-tight: 1.3;
  --theme-letter-spacing: 0;
}

/* 主题过渡动画 */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border-subtle);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-muted);
}

/* 选择文本样式 */
::selection {
  background: var(--theme-primary-muted);
  color: var(--theme-bg);
}

::-moz-selection {
  background: var(--theme-primary-muted);
  color: var(--theme-bg);
}
