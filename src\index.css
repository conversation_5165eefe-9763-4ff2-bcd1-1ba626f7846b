@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 引入主题系统 */
@import './styles/themes.css';
@import './styles/components.css';
@import './styles/icons.css';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  font-weight: var(--theme-font-weight-normal);
  line-height: var(--theme-line-height-normal);
  letter-spacing: var(--theme-letter-spacing);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--theme-bg);
  color: var(--theme-text);
  overflow: hidden;
}

#root {
  height: 100%;
  width: 100%;
}

/* 代码字体 */
code {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 按钮和交互元素的基础样式 */
button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 输入框基础样式 */
input, textarea {
  font-family: inherit;
  border: none;
  outline: none;
  background: transparent;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 动画和过渡 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-out-right {
  animation: slideOutRight 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .no-print {
    display: none !important;
  }
}
