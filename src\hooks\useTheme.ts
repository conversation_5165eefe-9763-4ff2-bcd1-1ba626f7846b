import { useState, useEffect } from 'react'

export type Theme = 'dark' | 'light'

const THEME_STORAGE_KEY = 'cloud-notes-theme'

export const useTheme = () => {
  // 从localStorage获取保存的主题，默认为深色主题
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) as Theme
      return savedTheme || 'dark'
    }
    return 'dark'
  })

  // 应用主题到DOM
  useEffect(() => {
    const root = document.documentElement
    
    if (theme === 'light') {
      root.setAttribute('data-theme', 'light')
    } else {
      root.removeAttribute('data-theme')
    }
    
    // 保存主题到localStorage
    localStorage.setItem(THEME_STORAGE_KEY, theme)
  }, [theme])

  // 切换主题
  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'dark' ? 'light' : 'dark')
  }

  // 设置特定主题
  const setSpecificTheme = (newTheme: Theme) => {
    setTheme(newTheme)
  }

  return {
    theme,
    toggleTheme,
    setTheme: setSpecificTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light'
  }
}
