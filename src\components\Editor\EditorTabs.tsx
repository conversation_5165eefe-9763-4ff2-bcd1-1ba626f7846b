import React from 'react'

export type EditorMode = 'edit' | 'preview'
export type FileType = 'markdown' | 'html'

interface EditorTabsProps {
  activeMode: EditorMode
  onModeChange: (mode: EditorMode) => void
  hasUnsavedChanges?: boolean
  disabled?: boolean
  fileType?: FileType
}

const EditorTabs: React.FC<EditorTabsProps> = ({
  activeMode,
  onModeChange,
  hasUnsavedChanges = false,
  disabled = false
}) => {
  const tabs = [
    {
      id: 'edit' as EditorMode,
      label: '编辑',
      description: '编辑内容'
    },
    {
      id: 'preview' as EditorMode,
      label: '预览',
      description: '预览结果'
    }
  ]

  return (
    <div className="flex items-center h-full">
      {tabs.map((tab) => {
        const isActive = activeMode === tab.id

        return (
          <button
            key={tab.id}
            onClick={() => !disabled && onModeChange(tab.id)}
            disabled={disabled}
            title={tab.description}
            className={`
              px-3 py-2 text-sm font-medium
              transition-colors duration-200
              ${isActive
                ? 'text-theme-text'
                : 'text-theme-text-muted hover:text-theme-text'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              focus:outline-none
            `}
          >
            {tab.label}

            {/* 未保存更改指示器 */}
            {tab.id === 'edit' && hasUnsavedChanges && (
              <span className="ml-2 w-2 h-2 bg-theme-warning rounded-full inline-block" />
            )}
          </button>
        )
      })}
    </div>
  )
}

export default EditorTabs
