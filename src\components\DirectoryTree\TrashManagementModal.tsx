import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useDirectoryStore } from '../../stores/directoryStore'
import { 
  Trash2, 
  RotateCcw, 
  X, 
  CheckSquare, 
  Square, 
  Folder, 
  FileText, 
  Code,
  AlertTriangle,
  Loader2
} from 'lucide-react'

interface TrashManagementModalProps {
  isOpen: boolean
  onClose: () => void
}

const TrashManagementModal: React.FC<TrashManagementModalProps> = ({
  isOpen,
  onClose
}) => {
  const { 
    trashItems, 
    loadTrashItems, 
    restoreItem, 
    permanentDeleteItem, 
    emptyTrash,
    loading 
  } = useDirectoryStore()
  
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState<{
    type: 'delete' | 'empty' | null
    items?: string[]
  }>({ type: null })

  // 加载回收站数据
  useEffect(() => {
    if (isOpen) {
      loadTrashItems()
      setSelectedItems(new Set())
    }
  }, [isOpen, loadTrashItems])

  // 获取文件类型图标
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'folder':
        return <Folder className="w-4 h-4 text-blue-400" />
      case 'note':
        return <FileText className="w-4 h-4 text-green-400" />
      case 'html':
        return <Code className="w-4 h-4 text-orange-400" />
      default:
        return <FileText className="w-4 h-4 text-gray-400" />
    }
  }

  // 格式化删除时间
  const formatDeletedTime = (deletedAt?: string) => {
    if (!deletedAt) return '未知时间'

    const date = new Date(deletedAt)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedItems.size === trashItems.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(trashItems.map(item => item._id)))
    }
  }

  // 切换单个项目选择
  const toggleItemSelection = (itemId: string) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedItems(newSelected)
  }

  // 恢复单个项目
  const handleRestoreItem = async (itemId: string) => {
    setIsLoading(true)
    try {
      await restoreItem(itemId)
      setSelectedItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    } catch (error) {
      console.error('恢复失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 永久删除单个项目
  const handlePermanentDeleteItem = async (itemId: string) => {
    setShowConfirmDialog({ type: 'delete', items: [itemId] })
  }

  // 批量恢复
  const handleBatchRestore = async () => {
    if (selectedItems.size === 0) return
    
    setIsLoading(true)
    try {
      const promises = Array.from(selectedItems).map(id => restoreItem(id))
      await Promise.all(promises)
      setSelectedItems(new Set())
    } catch (error) {
      console.error('批量恢复失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 批量永久删除
  const handleBatchDelete = () => {
    if (selectedItems.size === 0) return
    setShowConfirmDialog({ type: 'delete', items: Array.from(selectedItems) })
  }

  // 清空回收站
  const handleEmptyTrash = () => {
    setShowConfirmDialog({ type: 'empty' })
  }

  // 确认操作
  const handleConfirmAction = async () => {
    if (!showConfirmDialog.type) return
    
    setIsLoading(true)
    try {
      if (showConfirmDialog.type === 'delete' && showConfirmDialog.items) {
        const promises = showConfirmDialog.items.map(id => permanentDeleteItem(id))
        await Promise.all(promises)
        setSelectedItems(new Set())
      } else if (showConfirmDialog.type === 'empty') {
        await emptyTrash()
        setSelectedItems(new Set())
      }
    } catch (error) {
      console.error('操作失败:', error)
    } finally {
      setIsLoading(false)
      setShowConfirmDialog({ type: null })
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* 背景遮罩 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* 弹窗主体 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-4xl max-h-[80vh] mx-4 bg-theme-surface border border-theme-border rounded-xl shadow-2xl overflow-hidden"
        >
          {/* 弹窗头部 */}
          <div className="flex items-center justify-between p-6 border-b border-theme-border">
            <div className="flex items-center space-x-3">
              <Trash2 className="w-6 h-6 text-red-400" />
              <h2 className="text-xl font-semibold text-theme-text">
                回收站管理
              </h2>
              <span className="px-2 py-1 text-xs bg-theme-primary/10 text-theme-primary rounded-full">
                {trashItems.length} 个项目
              </span>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-theme-surface-hover rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-theme-text-secondary" />
            </button>
          </div>

          {/* 操作栏 */}
          <div className="flex items-center justify-between p-4 bg-theme-surface-hover/50 border-b border-theme-border">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSelectAll}
                className="flex items-center space-x-2 text-sm text-theme-text-secondary hover:text-theme-text transition-colors"
              >
                {selectedItems.size === trashItems.length ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
                <span>全选 ({selectedItems.size})</span>
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              {selectedItems.size > 0 && (
                <>
                  <button
                    onClick={handleBatchRestore}
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white text-sm rounded-lg transition-colors"
                  >
                    <RotateCcw className="w-4 h-4" />
                    <span>恢复选中</span>
                  </button>
                  <button
                    onClick={handleBatchDelete}
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-3 py-1.5 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white text-sm rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>永久删除</span>
                  </button>
                </>
              )}
              
              {trashItems.length > 0 && (
                <button
                  onClick={handleEmptyTrash}
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white text-sm rounded-lg transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>清空回收站</span>
                </button>
              )}
            </div>
          </div>

          {/* 文件列表 */}
          <div className="flex-1 overflow-y-auto max-h-96">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin text-theme-primary" />
                <span className="ml-2 text-theme-text-secondary">加载中...</span>
              </div>
            ) : trashItems.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-theme-text-secondary">
                <Trash2 className="w-12 h-12 mb-4 opacity-50" />
                <p>回收站为空</p>
              </div>
            ) : (
              <div className="divide-y divide-theme-border">
                {trashItems.map((item) => (
                  <div
                    key={item._id}
                    className="flex items-center p-4 hover:bg-theme-surface-hover transition-colors"
                  >
                    <button
                      onClick={() => toggleItemSelection(item._id)}
                      className="mr-3"
                    >
                      {selectedItems.has(item._id) ? (
                        <CheckSquare className="w-4 h-4 text-theme-primary" />
                      ) : (
                        <Square className="w-4 h-4 text-theme-text-secondary" />
                      )}
                    </button>
                    
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      {getFileIcon(item.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-theme-text truncate">
                          {item.name}
                        </p>
                        <p className="text-xs text-theme-text-secondary truncate">
                          {item.path}
                        </p>
                      </div>
                      <div className="text-xs text-theme-text-secondary">
                        {formatDeletedTime(item.deletedAt)}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleRestoreItem(item._id)}
                        disabled={isLoading}
                        className="p-1.5 hover:bg-green-600/20 text-green-400 hover:text-green-300 rounded transition-colors"
                        title="恢复"
                      >
                        <RotateCcw className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handlePermanentDeleteItem(item._id)}
                        disabled={isLoading}
                        className="p-1.5 hover:bg-red-600/20 text-red-400 hover:text-red-300 rounded transition-colors"
                        title="永久删除"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>

        {/* 确认对话框 */}
        {showConfirmDialog.type && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-black/70 flex items-center justify-center z-10"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-theme-surface border border-theme-border rounded-lg p-6 max-w-md mx-4"
            >
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="w-6 h-6 text-red-400" />
                <h3 className="text-lg font-semibold text-theme-text">
                  确认操作
                </h3>
              </div>
              
              <p className="text-theme-text-secondary mb-6">
                {showConfirmDialog.type === 'empty' 
                  ? '确定要清空回收站吗？此操作将永久删除所有项目，无法恢复。'
                  : `确定要永久删除选中的 ${showConfirmDialog.items?.length || 0} 个项目吗？此操作无法恢复。`
                }
              </p>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowConfirmDialog({ type: null })}
                  disabled={isLoading}
                  className="px-4 py-2 text-theme-text-secondary hover:text-theme-text transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmAction}
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
                  <span>确认删除</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </div>
    </AnimatePresence>
  )
}

export default TrashManagementModal
