#!/usr/bin/env node

/**
 * 云开发静态托管自动化部署脚本 - MCP集成版本
 * 使用云开发MCP工具进行部署前清理和单版本部署
 */

const fs = require('fs');
const path = require('path');

// 部署配置
const DEPLOY_CONFIG = {
  envId: 'ai-demo-8gjoyg63e237ce06',
  localPath: './dist',
  cloudPath: 'cloud-notes',
  domain: 'ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com',
  
  // 需要清理的旧版本目录
  cleanupDirectories: [
    'solar-notes',
    'cloud-admin', 
    'notes-app',
    'admin-panel',
    'old-notes'
  ],
  
  // 需要清理的文件模式
  cleanupPatterns: [
    // 旧版本的资源文件（不在当前部署目录中的）
    /^(?!cloud-notes\/).*\/assets\/.*-[a-zA-Z0-9]{8,}\.(js|css|ttf)$/,
    // 旧版本的入口文件
    /^(?!cloud-notes\/).*\/index-[a-zA-Z0-9]{8,}\.(js|css)$/,
    // 重复的编辑器文件
    /^(?!cloud-notes\/).*\/editor-[a-zA-Z0-9]{8,}\.(js|css)$/
  ]
};

/**
 * 日志工具
 */
const log = {
  info: (msg) => console.log(`\x1b[36m[INFO]\x1b[0m ${msg}`),
  success: (msg) => console.log(`\x1b[32m[✓]\x1b[0m ${msg}`),
  warning: (msg) => console.log(`\x1b[33m[⚠]\x1b[0m ${msg}`),
  error: (msg) => console.log(`\x1b[31m[✗]\x1b[0m ${msg}`),
  step: (step, total, msg) => console.log(`\x1b[35m[${step}/${total}]\x1b[0m ${msg}`)
};

/**
 * 部署前清理函数
 * 这个函数展示了清理逻辑，实际使用时需要通过MCP工具调用
 */
async function performPreDeployCleanup() {
  log.step(1, 4, '开始部署前清理...');
  
  try {
    // 步骤1: 获取所有文件列表
    log.info('获取静态托管文件列表...');
    
    // 实际使用时的MCP调用示例：
    // const allFiles = await mcpCall('findFiles', { prefix: '', maxKeys: 1000 });
    
    // 步骤2: 删除旧版本目录
    log.info('清理旧版本目录...');
    for (const dir of DEPLOY_CONFIG.cleanupDirectories) {
      try {
        // 实际使用时的MCP调用：
        // await mcpCall('deleteFiles', { cloudPath: dir, isDir: true });
        log.info(`已删除目录: ${dir}`);
      } catch (error) {
        log.warning(`删除目录失败 ${dir}: ${error.message}`);
      }
    }
    
    // 步骤3: 清理匹配模式的文件
    log.info('清理重复和过期文件...');
    // 这里会根据cleanupPatterns清理匹配的文件
    
    log.success('部署前清理完成');
    
  } catch (error) {
    log.error(`清理失败: ${error.message}`);
    throw error;
  }
}

/**
 * 执行部署
 */
async function performDeploy() {
  log.step(2, 4, '开始部署新版本...');
  
  try {
    // 检查本地构建目录
    if (!fs.existsSync(DEPLOY_CONFIG.localPath)) {
      throw new Error(`构建目录不存在: ${DEPLOY_CONFIG.localPath}`);
    }
    
    const localAbsPath = path.resolve(DEPLOY_CONFIG.localPath);
    log.info(`本地路径: ${localAbsPath}`);
    log.info(`云端路径: ${DEPLOY_CONFIG.cloudPath}`);
    
    // 实际使用时的MCP调用：
    // const result = await mcpCall('uploadFiles', {
    //   localPath: localAbsPath,
    //   cloudPath: DEPLOY_CONFIG.cloudPath
    // });
    
    log.success('新版本部署完成');
    
    return {
      filesUploaded: 0, // result.files?.length || 0
      accessUrl: `https://${DEPLOY_CONFIG.domain}/${DEPLOY_CONFIG.cloudPath}/`
    };
    
  } catch (error) {
    log.error(`部署失败: ${error.message}`);
    throw error;
  }
}

/**
 * 验证部署结果
 */
async function verifyDeployment() {
  log.step(3, 4, '验证部署结果...');
  
  try {
    // 检查关键文件是否存在
    const keyFiles = ['index.html', 'manifest.json'];
    
    for (const file of keyFiles) {
      const cloudPath = `${DEPLOY_CONFIG.cloudPath}/${file}`;
      // 实际使用时可以通过findFiles验证文件是否存在
      log.info(`验证文件: ${cloudPath}`);
    }
    
    const accessUrl = `https://${DEPLOY_CONFIG.domain}/${DEPLOY_CONFIG.cloudPath}/`;
    log.success(`部署验证通过! 访问地址: ${accessUrl}`);
    
    return accessUrl;
    
  } catch (error) {
    log.error(`部署验证失败: ${error.message}`);
    throw error;
  }
}

/**
 * 更新文档
 */
function updateDocumentation(accessUrl) {
  log.step(4, 4, '更新项目文档...');
  
  try {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) {
      log.warning('README.md 不存在，跳过文档更新');
      return;
    }
    
    let content = fs.readFileSync(readmePath, 'utf8');
    const timestamp = new Date().toISOString().split('T')[0];
    
    // 更新访问地址
    const urlPattern = /\*\*正式环境：\*\* \[https:\/\/[^\]]+\]/g;
    content = content.replace(urlPattern, `**正式环境：** [${accessUrl}](${accessUrl})`);
    
    // 添加或更新部署信息
    if (!content.includes('最后部署')) {
      const deployInfoPattern = /(## 🚀 部署信息[\s\S]*?)(### |##)/;
      if (deployInfoPattern.test(content)) {
        content = content.replace(
          deployInfoPattern,
          `$1- **最后部署**：${timestamp}\n- **部署策略**：单版本自动清理\n\n$2`
        );
      }
    } else {
      content = content.replace(
        /- \*\*最后部署\*\*：[^\n]+/g,
        `- **最后部署**：${timestamp}`
      );
    }
    
    fs.writeFileSync(readmePath, content, 'utf8');
    log.success('文档更新完成');
    
  } catch (error) {
    log.error(`文档更新失败: ${error.message}`);
  }
}

/**
 * 主部署流程
 */
async function main() {
  console.log('\n🚀 云开发静态托管自动化部署');
  console.log('=====================================');
  console.log(`环境ID: ${DEPLOY_CONFIG.envId}`);
  console.log(`部署路径: ${DEPLOY_CONFIG.cloudPath}`);
  console.log(`本地构建: ${DEPLOY_CONFIG.localPath}`);
  console.log('=====================================\n');
  
  try {
    // 1. 部署前清理
    await performPreDeployCleanup();
    
    // 2. 执行部署
    const deployResult = await performDeploy();
    
    // 3. 验证部署
    const accessUrl = await verifyDeployment();
    
    // 4. 更新文档
    updateDocumentation(accessUrl);
    
    // 部署摘要
    console.log('\n🎉 部署完成!');
    console.log('=====================================');
    console.log(`✅ 访问地址: ${accessUrl}`);
    console.log(`✅ 上传文件: ${deployResult.filesUploaded} 个`);
    console.log(`✅ 部署时间: ${new Date().toLocaleString()}`);
    console.log(`✅ 清理策略: 已清理 ${DEPLOY_CONFIG.cleanupDirectories.length} 个旧目录`);
    console.log('=====================================\n');
    
    // 提示用户
    console.log('💡 提示:');
    console.log('- CDN缓存可能需要几分钟刷新');
    console.log('- 如有缓存问题，请强制刷新浏览器 (Ctrl+F5)');
    console.log('- 文档已自动更新访问地址');
    
  } catch (error) {
    console.log('\n❌ 部署失败!');
    console.log('=====================================');
    console.log(`错误: ${error.message}`);
    console.log('=====================================\n');
    
    process.exit(1);
  }
}

// 导出配置和函数供其他脚本使用
module.exports = {
  DEPLOY_CONFIG,
  performPreDeployCleanup,
  performDeploy,
  verifyDeployment,
  updateDocumentation,
  main
};

// 如果直接运行此脚本
if (require.main === module) {
  main();
}
