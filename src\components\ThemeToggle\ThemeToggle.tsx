import React from 'react'
import { SunIcon, MoonIcon } from '../Icons/IconLibrary'
import { useTheme } from '../../hooks/useTheme'

interface ThemeToggleProps {
  className?: string
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { toggleTheme, isDark } = useTheme()

  return (
    <button
      onClick={toggleTheme}
      className={`
        flex items-center justify-center w-8 h-8 rounded-lg
        bg-theme-surface hover:bg-theme-surface-hover
        border border-theme-border hover:border-theme-border-subtle
        text-theme-text-muted hover:text-theme-text
        transition-all duration-200 ease-out
        focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2 focus:ring-offset-theme-bg
        active:scale-95 transform
        shadow-theme-sm hover:shadow-theme
        ${className}
      `}
      title={isDark ? '切换到浅色主题' : '切换到深色主题'}
      aria-label={isDark ? '切换到浅色主题' : '切换到深色主题'}
    >
      {isDark ? (
        <SunIcon className="theme-toggle-icon" />
      ) : (
        <MoonIcon className="theme-toggle-icon" />
      )}
    </button>
  )
}

export default ThemeToggle
