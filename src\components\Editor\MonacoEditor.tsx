import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react'
import * as monaco from 'monaco-editor'
import { useTheme } from '../../hooks/useTheme'

interface MonacoEditorProps {
  value: string
  onChange: (value: string) => void
  onSave?: () => void
  onScrollChange?: (scrollTop: number) => void
  language?: string
  theme?: string
  options?: monaco.editor.IStandaloneEditorConstructionOptions
  height?: string | number
  readOnly?: boolean
}

export interface MonacoEditorRef {
  focus: () => void
  getEditor: () => monaco.editor.IStandaloneCodeEditor | null
}

const MonacoEditor = forwardRef<MonacoEditorRef, MonacoEditorProps>(({
  value,
  onChange,
  onSave,
  onScrollChange,
  language = 'markdown',
  theme = 'vs-dark',
  options = {},
  height = '100%',
  readOnly = false
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const editorInstanceRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null)
  const { theme: currentTheme } = useTheme()

  // 暴露编辑器方法给父组件
  useImperativeHandle(ref, () => ({
    focus: () => {
      editorInstanceRef.current?.focus()
    },
    getEditor: () => editorInstanceRef.current
  }), [])

  useEffect(() => {
    if (editorRef.current) {
      // 创建编辑器实例
      editorInstanceRef.current = monaco.editor.create(editorRef.current, {
        value,
        language,
        theme,
        automaticLayout: true,
        minimap: { enabled: false },
        fontSize: 14,
        lineHeight: 1.6,
        wordWrap: 'on',
        scrollBeyondLastLine: false,
        padding: { top: 16, bottom: 16 },
        readOnly,
        ...options
      })

      // 监听内容变化
      editorInstanceRef.current.onDidChangeModelContent(() => {
        const currentValue = editorInstanceRef.current?.getValue() || ''
        onChange(currentValue)
      })

      // 监听滚动变化
      if (onScrollChange) {
        editorInstanceRef.current.onDidScrollChange((e) => {
          onScrollChange(e.scrollTop)
        })
      }

      // 添加保存快捷键
      if (onSave) {
        editorInstanceRef.current.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
          onSave()
        })
      }

      // 添加格式化快捷键（稍后定义）

      // 定义深色主题
      monaco.editor.defineTheme('cloud-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '7d8590', fontStyle: 'italic' },
          { token: 'keyword', foreground: '58a6ff' },
          { token: 'string', foreground: '7ee787' },
          { token: 'number', foreground: 'ffa657' },
          { token: 'type', foreground: '79c0ff' },
          { token: 'function', foreground: 'd2a8ff' }
        ],
        colors: {
          'editor.background': '#0d1117',
          'editor.foreground': '#f0f6fc',
          'editor.lineHighlightBackground': '#161b22',
          'editor.selectionBackground': '#264f78',
          'editorCursor.foreground': '#58a6ff',
          'editorLineNumber.foreground': '#7d8590',
          'editorLineNumber.activeForeground': '#f0f6fc',
          'editor.findMatchBackground': '#ffd33d44',
          'editor.findMatchHighlightBackground': '#ffd33d22'
        }
      })

      // 定义浅色主题
      monaco.editor.defineTheme('cloud-light', {
        base: 'vs',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '8c959f', fontStyle: 'italic' },
          { token: 'keyword', foreground: '0969da' },
          { token: 'string', foreground: '1a7f37' },
          { token: 'number', foreground: '9a6700' },
          { token: 'type', foreground: '0550ae' },
          { token: 'function', foreground: '8250df' }
        ],
        colors: {
          'editor.background': '#ffffff',
          'editor.foreground': '#24292f',
          'editor.lineHighlightBackground': '#f6f8fa',
          'editor.selectionBackground': '#add6ff',
          'editorCursor.foreground': '#0969da',
          'editorLineNumber.foreground': '#8c959f',
          'editorLineNumber.activeForeground': '#24292f',
          'editor.findMatchBackground': '#fff8c5',
          'editor.findMatchHighlightBackground': '#fff8c588'
        }
      })

      // 根据当前主题应用相应的编辑器主题
      const editorTheme = currentTheme === 'dark' ? 'cloud-dark' : 'cloud-light'
      monaco.editor.setTheme(editorTheme)
    }

    return () => {
      // 清理编辑器实例
      if (editorInstanceRef.current) {
        editorInstanceRef.current.dispose()
      }
    }
  }, [])

  // 当value从外部改变时更新编辑器内容
  useEffect(() => {
    if (editorInstanceRef.current) {
      const currentValue = editorInstanceRef.current.getValue()
      if (currentValue !== value) {
        // 保存当前光标位置
        const position = editorInstanceRef.current.getPosition()
        const scrollTop = editorInstanceRef.current.getScrollTop()

        // 更新内容
        editorInstanceRef.current.setValue(value)

        // 恢复光标位置（如果位置仍然有效）
        if (position) {
          const model = editorInstanceRef.current.getModel()
          if (model) {
            const lineCount = model.getLineCount()
            const validPosition = {
              lineNumber: Math.min(position.lineNumber, lineCount),
              column: position.column
            }
            editorInstanceRef.current.setPosition(validPosition)
          }
        }

        // 恢复滚动位置
        editorInstanceRef.current.setScrollTop(scrollTop)
      }
    }
  }, [value])

  // 当语言改变时更新编辑器语言
  useEffect(() => {
    if (editorInstanceRef.current) {
      const model = editorInstanceRef.current.getModel()
      if (model) {
        monaco.editor.setModelLanguage(model, language)
      }
    }
  }, [language])

  // 监听主题变化
  useEffect(() => {
    if (editorInstanceRef.current) {
      const editorTheme = currentTheme === 'dark' ? 'cloud-dark' : 'cloud-light'
      monaco.editor.setTheme(editorTheme)
    }
  }, [currentTheme])

  // 工具栏支持方法
  const insertTextAtCursor = (text: string, cursorOffset: number = 0) => {
    if (!editorInstanceRef.current) return

    const selection = editorInstanceRef.current.getSelection()
    if (!selection) return

    const range = new monaco.Range(
      selection.startLineNumber,
      selection.startColumn,
      selection.endLineNumber,
      selection.endColumn
    )

    const op = {
      range,
      text,
      forceMoveMarkers: true
    }

    editorInstanceRef.current.executeEdits('toolbar', [op])

    // 设置光标位置
    if (cursorOffset !== 0) {
      const newPosition = editorInstanceRef.current.getPosition()
      if (newPosition) {
        const newColumn = newPosition.column + cursorOffset
        editorInstanceRef.current.setPosition({
          lineNumber: newPosition.lineNumber,
          column: Math.max(1, newColumn)
        })
      }
    }

    editorInstanceRef.current.focus()
  }

  const insertTextAroundSelection = (prefix: string, suffix: string) => {
    if (!editorInstanceRef.current) return

    const selection = editorInstanceRef.current.getSelection()
    if (!selection) return

    const selectedText = editorInstanceRef.current.getModel()?.getValueInRange(selection) || ''
    const newText = `${prefix}${selectedText}${suffix}`

    const range = new monaco.Range(
      selection.startLineNumber,
      selection.startColumn,
      selection.endLineNumber,
      selection.endColumn
    )

    const op = {
      range,
      text: newText,
      forceMoveMarkers: true
    }

    editorInstanceRef.current.executeEdits('toolbar', [op])

    // 如果没有选中文本，将光标放在前缀和后缀之间
    if (!selectedText) {
      const newPosition = editorInstanceRef.current.getPosition()
      if (newPosition) {
        editorInstanceRef.current.setPosition({
          lineNumber: newPosition.lineNumber,
          column: newPosition.column - suffix.length
        })
      }
    }

    editorInstanceRef.current.focus()
  }

  // 在编辑器创建后绑定快捷键
  React.useEffect(() => {
    if (editorInstanceRef.current) {
      // 添加格式化快捷键
      editorInstanceRef.current.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyB, () => {
        insertTextAroundSelection('**', '**')
      })

      editorInstanceRef.current.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI, () => {
        insertTextAroundSelection('*', '*')
      })

      editorInstanceRef.current.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK, () => {
        insertTextAtCursor('[链接文本](https://example.com)', -21)
      })
    }
  }, [])

  return (
    <div
      ref={editorRef}
      style={{
        height: typeof height === 'number' ? `${height}px` : height,
        width: '100%'
      }}
      className="border-0 outline-none font-theme-normal"
    />
  )
})

MonacoEditor.displayName = 'MonacoEditor'

export default MonacoEditor
