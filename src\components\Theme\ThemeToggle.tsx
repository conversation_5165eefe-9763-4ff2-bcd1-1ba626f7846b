import React from 'react'
import { motion } from 'framer-motion'
import { Sun } from 'lucide-react'
import { useThemeStore } from '../../stores/themeStore'

interface ThemeToggleProps {
  className?: string
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { themes } = useThemeStore()
  const currentTheme = themes.solar // 始终使用太阳系主题

  return (
    <div className={`relative ${className}`}>
      {/* 太阳系主题指示器 */}
      <div
        className="p-2 rounded-full bg-theme-primary/10 border border-theme-primary/20"
        title={`当前主题: ${currentTheme.name}`}
      >
        <motion.div
          whileHover={{ rotate: 180 }}
          transition={{ duration: 0.3 }}
          className="text-theme-primary"
        >
          <Sun className="w-4 h-4" />
        </motion.div>
      </div>
    </div>
  )
}

export default ThemeToggle
