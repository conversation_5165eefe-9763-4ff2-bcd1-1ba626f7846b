const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const collection = db.collection('directory_tree')

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'getTree':
      case 'getDirectoryTree':
        return await getDirectoryTree()
      case 'getItem':
        return await getItem(data)
      case 'createItem':
        return await createItem(data)
      case 'updateItem':
        return await updateItem(data)
      case 'deleteItem':
        return await deleteItem(data)
      case 'moveItem':
        return await moveItem(data)
      case 'getRecentFiles':
        return await getRecentFiles()
      case 'getSharedFiles':
        return await getSharedFiles()
      case 'getTrashItems':
        return await getTrashItems()
      case 'restoreItem':
        return await restoreItem(data)
      case 'permanentDelete':
        return await permanentDelete(data)
      case 'emptyTrash':
        return await emptyTrash()
      default:
        throw new Error(`Unknown action: ${action}`)
    }
  } catch (error) {
    console.error('Directory tree operation failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取完整目录树（排除已删除的项目）
 */
async function getDirectoryTree() {
  try {
    const result = await collection
      .where({
        isDeleted: db.command.neq(true)
      })
      .orderBy('level', 'asc')
      .orderBy('name', 'asc')
      .get()

    // 构建树形结构
    const items = result.data
    const tree = buildTree(items)

    return {
      success: true,
      data: tree
    }
  } catch (error) {
    throw new Error(`Failed to get directory tree: ${error.message}`)
  }
}

/**
 * 构建树形结构
 */
function buildTree(items) {
  const itemMap = new Map()
  const rootItems = []
  
  // 创建映射
  items.forEach(item => {
    itemMap.set(item._id, { ...item, children: [] })
  })
  
  // 构建父子关系
  items.forEach(item => {
    if (item.parentId && itemMap.has(item.parentId)) {
      itemMap.get(item.parentId).children.push(itemMap.get(item._id))
    } else if (!item.parentId || item.parentId === 'root') {
      if (item._id !== 'root') {
        rootItems.push(itemMap.get(item._id))
      }
    }
  })
  
  return itemMap.get('root') ? [itemMap.get('root')] : rootItems
}

/**
 * 获取单个项目的详细信息
 */
async function getItem(data) {
  const { id } = data

  try {
    const result = await collection.doc(id).get()

    if (result.data) {
      return {
        success: true,
        data: result.data
      }
    } else {
      throw new Error('Item not found')
    }
  } catch (error) {
    throw new Error(`Failed to get item: ${error.message}`)
  }
}

/**
 * 创建新项目（文件夹、笔记、HTML文件）
 */
async function createItem(data) {
  const { name, type, parentId = 'root', content = '' } = data
  
  // 生成唯一ID
  const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  // 获取父级信息
  let parentPath = '/'
  let level = 1
  
  if (parentId && parentId !== 'root') {
    const parent = await collection.doc(parentId).get()
    if (parent.data) {
      parentPath = parent.data.path
      level = parent.data.level + 1
    }
  }
  
  // 构建路径
  const path = parentPath === '/' ? `/${name}` : `${parentPath}/${name}`
  
  // 创建新项目
  const newItem = {
    _id: id,
    name,
    type,
    parentId,
    path,
    level,
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'current-user'
  }
  
  // 根据类型添加特定字段
  if (type === 'folder') {
    newItem.isExpanded = false
  } else {
    newItem.content = content
    newItem.isShared = false
    newItem.importance = 5
  }
  
  await collection.add({
    data: newItem
  })
  
  return {
    success: true,
    data: newItem
  }
}

/**
 * 更新项目
 */
async function updateItem(data) {
  const { id, updates } = data
  
  const updateData = {
    ...updates,
    updatedAt: new Date().toISOString()
  }
  
  await collection.doc(id).update({
    data: updateData
  })
  
  return {
    success: true,
    data: { id, ...updateData }
  }
}

/**
 * 删除项目（软删除，移到回收站）
 */
async function deleteItem(data) {
  const { id } = data

  // 递归软删除子项目
  await softDeleteItemRecursive(id)

  return {
    success: true,
    data: { id }
  }
}

/**
 * 递归软删除项目及其子项目
 */
async function softDeleteItemRecursive(id) {
  // 查找所有子项目（未删除的）
  const children = await collection.where({
    parentId: id,
    isDeleted: db.command.neq(true)
  }).get()

  // 递归软删除子项目
  for (const child of children.data) {
    await softDeleteItemRecursive(child._id)
  }

  // 软删除当前项目
  await collection.doc(id).update({
    data: {
      isDeleted: true,
      deletedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
}

/**
 * 移动项目
 */
async function moveItem(data) {
  const { id, newParentId } = data
  
  // 获取项目信息
  const item = await collection.doc(id).get()
  if (item.data.length === 0) {
    throw new Error('Item not found')
  }
  
  const itemData = item.data[0]
  
  // 获取新父级信息
  let newParentPath = '/'
  let newLevel = 1
  
  if (newParentId && newParentId !== 'root') {
    const newParent = await collection.doc(newParentId).get()
    if (newParent.data.length > 0) {
      newParentPath = newParent.data[0].path
      newLevel = newParent.data[0].level + 1
    }
  }
  
  // 构建新路径
  const newPath = newParentPath === '/' ? `/${itemData.name}` : `${newParentPath}/${itemData.name}`
  
  // 更新项目
  await collection.doc(id).update({
    data: {
      parentId: newParentId,
      path: newPath,
      level: newLevel,
      updatedAt: new Date().toISOString()
    }
  })
  
  // 递归更新子项目的路径和层级
  await updateChildrenPaths(id, newPath, newLevel)
  
  return {
    success: true,
    data: { id, newParentId, newPath, newLevel }
  }
}

/**
 * 递归更新子项目的路径和层级
 */
async function updateChildrenPaths(parentId, parentPath, parentLevel) {
  const children = await collection.where({
    parentId: parentId
  }).get()
  
  for (const child of children.data) {
    const newPath = `${parentPath}/${child.name}`
    const newLevel = parentLevel + 1
    
    await collection.doc(child._id).update({
      data: {
        path: newPath,
        level: newLevel,
        updatedAt: new Date().toISOString()
      }
    })
    
    // 递归更新子项目的子项目
    await updateChildrenPaths(child._id, newPath, newLevel)
  }
}

/**
 * 获取最近文件
 */
async function getRecentFiles() {
  try {
    const result = await collection
      .where({
        type: db.command.in(['note', 'html']),
        isDeleted: db.command.neq(true)
      })
      .orderBy('updatedAt', 'desc')
      .limit(10)
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    throw new Error(`Failed to get recent files: ${error.message}`)
  }
}

/**
 * 获取分享文件
 */
async function getSharedFiles() {
  try {
    const result = await collection
      .where({
        type: db.command.in(['note', 'html']),
        isShared: true,
        isDeleted: db.command.neq(true)
      })
      .orderBy('updatedAt', 'desc')
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    throw new Error(`Failed to get shared files: ${error.message}`)
  }
}

/**
 * 获取回收站项目
 */
async function getTrashItems() {
  try {
    const result = await collection
      .where({
        isDeleted: true
      })
      .orderBy('deletedAt', 'desc')
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    throw new Error(`Failed to get trash items: ${error.message}`)
  }
}

/**
 * 恢复项目从回收站
 */
async function restoreItem(data) {
  const { id } = data

  try {
    // 获取要恢复的项目信息
    const itemResult = await collection.doc(id).get()
    if (!itemResult.data) {
      throw new Error('Item not found')
    }

    const itemData = itemResult.data

    // 检查父级是否存在且未删除
    if (itemData.parentId && itemData.parentId !== 'root') {
      const parentResult = await collection.doc(itemData.parentId).get()
      if (!parentResult.data || parentResult.data.isDeleted) {
        // 如果父级不存在或已删除，恢复到根目录
        await collection.doc(id).update({
          data: {
            parentId: 'root',
            path: `/${itemData.name}`,
            level: 1,
            isDeleted: false,
            deletedAt: null,
            updatedAt: new Date().toISOString()
          }
        })
      } else {
        // 恢复到原位置
        await collection.doc(id).update({
          data: {
            isDeleted: false,
            deletedAt: null,
            updatedAt: new Date().toISOString()
          }
        })
      }
    } else {
      // 恢复到根目录
      await collection.doc(id).update({
        data: {
          isDeleted: false,
          deletedAt: null,
          updatedAt: new Date().toISOString()
        }
      })
    }

    // 递归恢复子项目
    await restoreChildrenRecursive(id)

    return {
      success: true,
      data: { id }
    }
  } catch (error) {
    throw new Error(`Failed to restore item: ${error.message}`)
  }
}

/**
 * 递归恢复子项目
 */
async function restoreChildrenRecursive(parentId) {
  const children = await collection.where({
    parentId: parentId,
    isDeleted: true
  }).get()

  for (const child of children.data) {
    await collection.doc(child._id).update({
      data: {
        isDeleted: false,
        deletedAt: null,
        updatedAt: new Date().toISOString()
      }
    })

    // 递归恢复子项目的子项目
    await restoreChildrenRecursive(child._id)
  }
}

/**
 * 永久删除项目
 */
async function permanentDelete(data) {
  const { id } = data

  try {
    // 递归永久删除子项目
    await permanentDeleteRecursive(id)

    return {
      success: true,
      data: { id }
    }
  } catch (error) {
    throw new Error(`Failed to permanently delete item: ${error.message}`)
  }
}

/**
 * 递归永久删除项目及其子项目
 */
async function permanentDeleteRecursive(id) {
  // 查找所有子项目（已删除的）
  const children = await collection.where({
    parentId: id,
    isDeleted: true
  }).get()

  // 递归永久删除子项目
  for (const child of children.data) {
    await permanentDeleteRecursive(child._id)
  }

  // 永久删除当前项目
  await collection.doc(id).remove()
}

/**
 * 清空回收站
 */
async function emptyTrash() {
  try {
    const trashItems = await collection.where({
      isDeleted: true
    }).get()

    // 永久删除所有回收站项目
    for (const item of trashItems.data) {
      await collection.doc(item._id).remove()
    }

    return {
      success: true,
      data: { deletedCount: trashItems.data.length }
    }
  } catch (error) {
    throw new Error(`Failed to empty trash: ${error.message}`)
  }
}
