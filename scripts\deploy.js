#!/usr/bin/env node

/**
 * 云开发静态托管自动化部署脚本
 * 功能：部署前清理 + 单版本部署 + 文档更新
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const CONFIG = {
  // 云开发环境ID
  envId: 'ai-demo-8gjoyg63e237ce06',
  
  // 部署配置
  deploy: {
    localPath: './dist',           // 本地构建目录
    cloudPath: 'cloud-notes',      // 云端部署目录
    domain: 'ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com'
  },
  
  // 清理配置
  cleanup: {
    // 需要完全删除的目录（旧版本）
    deleteDirectories: [
      'solar-notes',
      'cloud-admin',
      'notes-app',
      'admin-panel'
    ],
    
    // 需要清理的文件模式（正则表达式）
    cleanupPatterns: [
      /assets\/.*-[a-zA-Z0-9]{8,}\.(js|css|ttf|woff|woff2)$/,  // 带hash的资源文件
      /index-[a-zA-Z0-9]{8,}\.(js|css)$/,                      // 带hash的入口文件
      /editor-[a-zA-Z0-9]{8,}\.(js|css)$/,                     // 编辑器文件
      /vendor-[a-zA-Z0-9]{8,}\.js$/                            // vendor文件
    ],
    
    // 保留的文件（即使匹配清理模式也不删除）
    preserveFiles: [
      'manifest.json',
      'robots.txt',
      '.well-known/'
    ]
  }
};

/**
 * 日志工具
 */
class Logger {
  static info(message) {
    console.log(`\x1b[36m[INFO]\x1b[0m ${message}`);
  }
  
  static success(message) {
    console.log(`\x1b[32m[SUCCESS]\x1b[0m ${message}`);
  }
  
  static warning(message) {
    console.log(`\x1b[33m[WARNING]\x1b[0m ${message}`);
  }
  
  static error(message) {
    console.log(`\x1b[31m[ERROR]\x1b[0m ${message}`);
  }
  
  static step(step, total, message) {
    console.log(`\x1b[35m[${step}/${total}]\x1b[0m ${message}`);
  }
}

/**
 * 云开发操作工具
 */
class CloudBaseManager {
  constructor(envId) {
    this.envId = envId;
  }
  
  /**
   * 获取所有文件列表
   */
  async getAllFiles() {
    try {
      Logger.info('获取静态托管文件列表...');
      
      // 这里需要调用MCP工具，暂时用模拟数据
      // 实际使用时需要集成MCP findFiles工具
      const result = await this.callMCP('findFiles', {
        prefix: '',
        maxKeys: 1000
      });
      
      return result.Contents || [];
    } catch (error) {
      Logger.error(`获取文件列表失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 删除指定文件或目录
   */
  async deleteFiles(files) {
    if (!files || files.length === 0) {
      Logger.info('没有需要删除的文件');
      return;
    }
    
    Logger.info(`准备删除 ${files.length} 个文件/目录...`);
    
    for (const file of files) {
      try {
        await this.callMCP('deleteFiles', {
          cloudPath: file.Key || file,
          isDir: file.isDirectory || false
        });
        Logger.info(`已删除: ${file.Key || file}`);
      } catch (error) {
        Logger.warning(`删除失败 ${file.Key || file}: ${error.message}`);
      }
    }
  }
  
  /**
   * 上传文件到静态托管
   */
  async uploadFiles(localPath, cloudPath) {
    try {
      Logger.info(`开始上传 ${localPath} 到 ${cloudPath}...`);
      
      const result = await this.callMCP('uploadFiles', {
        localPath: path.resolve(localPath),
        cloudPath: cloudPath
      });
      
      Logger.success(`上传完成! 共上传 ${result.files?.length || 0} 个文件`);
      return result;
    } catch (error) {
      Logger.error(`上传失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 模拟MCP调用（实际使用时需要替换为真实的MCP调用）
   */
  async callMCP(tool, params) {
    // 这里是模拟实现，实际使用时需要集成真实的MCP调用
    Logger.info(`调用MCP工具: ${tool}`);
    return {};
  }
}

/**
 * 部署清理器
 */
class DeploymentCleaner {
  constructor(config) {
    this.config = config;
    this.cloudbase = new CloudBaseManager(config.envId);
  }
  
  /**
   * 分析需要清理的文件
   */
  analyzeCleanupFiles(allFiles) {
    const toDelete = [];
    const toKeep = [];
    
    for (const file of allFiles) {
      const key = file.Key;
      
      // 检查是否是需要删除的目录
      const shouldDeleteDirectory = this.config.cleanup.deleteDirectories.some(dir => 
        key.startsWith(dir + '/')
      );
      
      if (shouldDeleteDirectory) {
        toDelete.push(file);
        continue;
      }
      
      // 检查是否匹配清理模式
      const matchesCleanupPattern = this.config.cleanup.cleanupPatterns.some(pattern => 
        pattern.test(key)
      );
      
      // 检查是否是保留文件
      const isPreservedFile = this.config.cleanup.preserveFiles.some(preserve => 
        key.includes(preserve)
      );
      
      if (matchesCleanupPattern && !isPreservedFile) {
        // 额外检查：不删除当前部署目录中的最新文件
        if (!key.startsWith(this.config.deploy.cloudPath + '/')) {
          toDelete.push(file);
        } else {
          toKeep.push(file);
        }
      } else {
        toKeep.push(file);
      }
    }
    
    return { toDelete, toKeep };
  }
  
  /**
   * 执行清理
   */
  async performCleanup() {
    Logger.step(1, 4, '开始部署前清理...');
    
    // 获取所有文件
    const allFiles = await this.cloudbase.getAllFiles();
    Logger.info(`发现 ${allFiles.length} 个文件`);
    
    // 分析需要清理的文件
    const { toDelete, toKeep } = this.analyzeCleanupFiles(allFiles);
    
    Logger.info(`需要删除: ${toDelete.length} 个文件`);
    Logger.info(`保留文件: ${toKeep.length} 个文件`);
    
    // 显示将要删除的文件
    if (toDelete.length > 0) {
      Logger.info('将要删除的文件:');
      toDelete.forEach(file => {
        console.log(`  - ${file.Key}`);
      });
      
      // 执行删除
      await this.cloudbase.deleteFiles(toDelete);
      Logger.success('清理完成!');
    } else {
      Logger.info('没有需要清理的文件');
    }
  }
}

/**
 * 文档更新器
 */
class DocumentationUpdater {
  constructor(config) {
    this.config = config;
  }
  
  /**
   * 更新README.md中的访问地址
   */
  updateReadme(deployResult) {
    Logger.step(4, 4, '更新文档...');
    
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) {
      Logger.warning('README.md 文件不存在，跳过文档更新');
      return;
    }
    
    try {
      let content = fs.readFileSync(readmePath, 'utf8');
      
      // 构建新的访问地址
      const newUrl = `https://${this.config.deploy.domain}/${this.config.deploy.cloudPath}/`;
      const timestamp = new Date().toISOString().split('T')[0];
      
      // 更新访问地址
      content = content.replace(
        /\*\*正式环境：\*\* \[https:\/\/[^\]]+\]/g,
        `**正式环境：** [${newUrl}](${newUrl})`
      );
      
      // 更新部署时间（如果存在相关标记）
      content = content.replace(
        /- \*\*最后部署\*\*：[^\n]+/g,
        `- **最后部署**：${timestamp}`
      );
      
      // 如果没有部署时间标记，在部署信息部分添加
      if (!content.includes('最后部署')) {
        content = content.replace(
          /(## 🚀 部署信息[\s\S]*?)(### |##)/,
          `$1- **最后部署**：${timestamp}\n- **部署版本**：v${require('../package.json').version || '1.0.0'}\n\n$2`
        );
      }
      
      fs.writeFileSync(readmePath, content, 'utf8');
      Logger.success('README.md 更新完成');
      
    } catch (error) {
      Logger.error(`更新README.md失败: ${error.message}`);
    }
  }
}

/**
 * 主部署器
 */
class MainDeployer {
  constructor(config) {
    this.config = config;
    this.cleaner = new DeploymentCleaner(config);
    this.cloudbase = new CloudBaseManager(config.envId);
    this.docUpdater = new DocumentationUpdater(config);
  }
  
  /**
   * 检查部署前置条件
   */
  checkPrerequisites() {
    Logger.step(0, 4, '检查部署前置条件...');
    
    // 检查构建目录是否存在
    if (!fs.existsSync(this.config.deploy.localPath)) {
      throw new Error(`构建目录不存在: ${this.config.deploy.localPath}`);
    }
    
    // 检查构建目录是否有内容
    const files = fs.readdirSync(this.config.deploy.localPath);
    if (files.length === 0) {
      throw new Error(`构建目录为空: ${this.config.deploy.localPath}`);
    }
    
    Logger.success('前置条件检查通过');
  }
  
  /**
   * 执行完整部署流程
   */
  async deploy() {
    try {
      Logger.info('🚀 开始自动化部署流程...');
      Logger.info(`环境: ${this.config.envId}`);
      Logger.info(`部署路径: ${this.config.deploy.cloudPath}`);
      
      // 0. 检查前置条件
      this.checkPrerequisites();
      
      // 1. 部署前清理
      await this.cleaner.performCleanup();
      
      // 2. 上传新版本
      Logger.step(2, 4, '上传新版本...');
      const deployResult = await this.cloudbase.uploadFiles(
        this.config.deploy.localPath,
        this.config.deploy.cloudPath
      );
      
      // 3. 验证部署
      Logger.step(3, 4, '验证部署...');
      const finalUrl = `https://${this.config.deploy.domain}/${this.config.deploy.cloudPath}/`;
      Logger.success(`部署完成! 访问地址: ${finalUrl}`);
      
      // 4. 更新文档
      this.docUpdater.updateReadme(deployResult);
      
      Logger.success('🎉 自动化部署流程完成!');
      
      return {
        success: true,
        url: finalUrl,
        filesUploaded: deployResult.files?.length || 0
      };
      
    } catch (error) {
      Logger.error(`部署失败: ${error.message}`);
      throw error;
    }
  }
}

// 主函数
async function main() {
  try {
    const deployer = new MainDeployer(CONFIG);
    const result = await deployer.deploy();
    
    console.log('\n✅ 部署摘要:');
    console.log(`   访问地址: ${result.url}`);
    console.log(`   上传文件: ${result.filesUploaded} 个`);
    console.log(`   部署时间: ${new Date().toLocaleString()}`);
    
    process.exit(0);
  } catch (error) {
    Logger.error(`部署失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { MainDeployer, CONFIG };
