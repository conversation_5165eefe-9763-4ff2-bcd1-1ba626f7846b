const cloudbase = require('@cloudbase/node-sdk')

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
})

const db = app.database()

/**
 * 笔记管理云函数
 * 支持笔记的增删改查操作
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'list':
        return await handleList(data)
      case 'get':
        return await handleGet(data)
      case 'create':
        return await handleCreate(data)
      case 'update':
        return await handleUpdate(data)
      case 'delete':
        return await handleDelete(data)
      case 'search':
        return await handleSearch(data)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('笔记操作错误:', error)
    return {
      success: false,
      message: error.message || '笔记服务异常'
    }
  }
}

/**
 * 获取笔记列表
 */
async function handleList({ userId, folderId, category, limit = 20, offset = 0 }) {
  const query = db.collection('notes')
  
  // 构建查询条件
  const where = { createdBy: userId }
  
  if (folderId) {
    where.folderId = folderId
  }
  
  if (category && category !== 'all') {
    where.category = category
  }

  const result = await query
    .where(where)
    .orderBy('updatedAt', 'desc')
    .skip(offset)
    .limit(limit)
    .get()

  return {
    success: true,
    data: {
      notes: result.data,
      total: result.data.length
    }
  }
}

/**
 * 获取单个笔记
 */
async function handleGet({ noteId, userId }) {
  const result = await db.collection('notes')
    .doc(noteId)
    .get()

  if (result.data.length === 0) {
    return {
      success: false,
      message: '笔记不存在'
    }
  }

  const note = result.data[0]
  
  // 检查权限
  if (note.createdBy !== userId) {
    return {
      success: false,
      message: '无权访问此笔记'
    }
  }

  return {
    success: true,
    data: { note }
  }
}

/**
 * 创建笔记
 */
async function handleCreate({ userId, noteData }) {
  const now = new Date().toISOString()
  
  const newNote = {
    ...noteData,
    createdBy: userId,
    createdAt: now,
    updatedAt: now,
    isShared: false
  }

  const result = await db.collection('notes').add(newNote)

  return {
    success: true,
    message: '笔记创建成功',
    data: {
      noteId: result.id,
      note: { ...newNote, _id: result.id }
    }
  }
}

/**
 * 更新笔记
 */
async function handleUpdate({ noteId, userId, updates }) {
  // 先检查笔记是否存在和权限
  const existingNote = await db.collection('notes')
    .doc(noteId)
    .get()

  if (existingNote.data.length === 0) {
    return {
      success: false,
      message: '笔记不存在'
    }
  }

  if (existingNote.data[0].createdBy !== userId) {
    return {
      success: false,
      message: '无权修改此笔记'
    }
  }

  // 更新笔记
  const updateData = {
    ...updates,
    updatedAt: new Date().toISOString()
  }

  await db.collection('notes')
    .doc(noteId)
    .update(updateData)

  return {
    success: true,
    message: '笔记更新成功'
  }
}

/**
 * 删除笔记
 */
async function handleDelete({ noteId, userId }) {
  // 先检查笔记是否存在和权限
  const existingNote = await db.collection('notes')
    .doc(noteId)
    .get()

  if (existingNote.data.length === 0) {
    return {
      success: false,
      message: '笔记不存在'
    }
  }

  if (existingNote.data[0].createdBy !== userId) {
    return {
      success: false,
      message: '无权删除此笔记'
    }
  }

  // 删除笔记
  await db.collection('notes')
    .doc(noteId)
    .remove()

  return {
    success: true,
    message: '笔记删除成功'
  }
}

/**
 * 搜索笔记
 */
async function handleSearch({ userId, keyword, limit = 20 }) {
  if (!keyword) {
    return {
      success: false,
      message: '搜索关键词不能为空'
    }
  }

  // 使用正则表达式进行模糊搜索
  const result = await db.collection('notes')
    .where({
      createdBy: userId,
      $or: [
        { title: new RegExp(keyword, 'i') },
        { content: new RegExp(keyword, 'i') }
      ]
    })
    .orderBy('updatedAt', 'desc')
    .limit(limit)
    .get()

  return {
    success: true,
    data: {
      notes: result.data,
      keyword,
      total: result.data.length
    }
  }
}
