<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 太阳核心 -->
  <circle cx="32" cy="32" r="16" fill="url(#sunGradient)" />
  
  <!-- 太阳光芒 -->
  <g stroke="#FDB813" stroke-width="2" stroke-linecap="round">
    <line x1="32" y1="4" x2="32" y2="12" />
    <line x1="32" y1="52" x2="32" y2="60" />
    <line x1="4" y1="32" x2="12" y2="32" />
    <line x1="52" y1="32" x2="60" y2="32" />
    <line x1="11.76" y1="11.76" x2="17.41" y2="17.41" />
    <line x1="46.59" y1="46.59" x2="52.24" y2="52.24" />
    <line x1="52.24" y1="11.76" x2="46.59" y2="17.41" />
    <line x1="17.41" y1="46.59" x2="11.76" y2="52.24" />
  </g>
  
  <!-- 行星轨道 -->
  <circle cx="32" cy="32" r="24" fill="none" stroke="#4169E1" stroke-width="1" opacity="0.3" />
  <circle cx="32" cy="32" r="28" fill="none" stroke="#CD5C5C" stroke-width="1" opacity="0.2" />
  
  <!-- 小行星 -->
  <circle cx="56" cy="32" r="2" fill="#4169E1" />
  <circle cx="8" cy="32" r="1.5" fill="#CD5C5C" />
  
  <!-- 渐变定义 -->
  <defs>
    <radialGradient id="sunGradient" cx="0.3" cy="0.3">
      <stop offset="0%" stop-color="#FFF5B4" />
      <stop offset="70%" stop-color="#FDB813" />
      <stop offset="100%" stop-color="#FF8C00" />
    </radialGradient>
  </defs>
</svg>
