import React, { createContext, useContext, useState, ReactNode } from 'react'

export type EditorMode = 'edit' | 'preview'

interface EditorModeContextType {
  editorMode: EditorMode
  setEditorMode: (mode: EditorMode) => void
}

const EditorModeContext = createContext<EditorModeContextType | undefined>(undefined)

export const useEditorMode = () => {
  const context = useContext(EditorModeContext)
  if (context === undefined) {
    throw new Error('useEditorMode must be used within an EditorModeProvider')
  }
  return context
}

interface EditorModeProviderProps {
  children: ReactNode
}

export const EditorModeProvider: React.FC<EditorModeProviderProps> = ({ children }) => {
  const [editorMode, setEditorMode] = useState<EditorMode>('preview')

  return (
    <EditorModeContext.Provider value={{ editorMode, setEditorMode }}>
      {children}
    </EditorModeContext.Provider>
  )
}
