import React from 'react'

const AdminPanel: React.FC = () => {
  return (
    <div className="min-h-screen bg-cosmic-void starfield-bg p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-sun-corona mb-8">
          太阳系管理控制台
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 用户统计 */}
          <div className="bg-starfield/80 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-venus-cloud mb-4">用户统计</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">总用户数</span>
                <span className="text-sun-corona font-bold">1</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">活跃用户</span>
                <span className="text-earth-blue font-bold">1</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">今日登录</span>
                <span className="text-earth-green font-bold">1</span>
              </div>
            </div>
          </div>

          {/* 笔记统计 */}
          <div className="bg-starfield/80 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-venus-cloud mb-4">笔记统计</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">总笔记数</span>
                <span className="text-sun-corona font-bold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">Markdown</span>
                <span className="text-earth-blue font-bold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">HTML</span>
                <span className="text-mars-red font-bold">0</span>
              </div>
            </div>
          </div>

          {/* 系统状态 */}
          <div className="bg-starfield/80 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-venus-cloud mb-4">系统状态</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">数据库</span>
                <span className="text-earth-green font-bold">正常</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">存储</span>
                <span className="text-earth-green font-bold">正常</span>
              </div>
              <div className="flex justify-between">
                <span className="text-venus-cloud/80">云函数</span>
                <span className="text-earth-green font-bold">正常</span>
              </div>
            </div>
          </div>
        </div>

        {/* 功能开发中提示 */}
        <div className="mt-8 bg-jupiter-storm/20 border border-jupiter-storm/40 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-jupiter-storm mb-2">
            🚧 功能开发中
          </h3>
          <p className="text-venus-cloud/80">
            管理员控制台的详细功能正在开发中，包括用户管理、系统监控、数据分析等功能。
          </p>
        </div>
      </div>
    </div>
  )
}

export default AdminPanel
