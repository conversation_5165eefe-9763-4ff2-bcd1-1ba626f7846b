import cloudbase from '@cloudbase/js-sdk'

// CloudBase配置
const app = cloudbase.init({
  env: import.meta.env.VITE_CLOUDBASE_ENV_ID || 'ai-demo-8gjoyg63e237ce06'
})

const auth = app.auth({
  persistence: 'local' as any
})
const db = app.database()

// 数据库集合名称
export const COLLECTIONS = {
  USERS: 'users',
  NOTES: 'notes', 
  FOLDERS: 'folders',
  SHARES: 'shares'
} as const

// 认证相关
export const authService = {
  // 检查登录状态
  async checkLoginState() {
    try {
      const loginState = await auth.getLoginState()
      return loginState && loginState.user !== null
    } catch (error) {
      console.error('检查登录状态失败:', error)
      return false
    }
  },

  // 匿名登录
  async signInAnonymously() {
    try {
      await auth.signInAnonymously()
      return true
    } catch (error) {
      console.error('匿名登录失败:', error)
      return false
    }
  },

  // 自定义登录（暂时禁用，等待API确认）
  async signInWithCustom(_ticket: string) {
    try {
      // TODO: 确认2.0版本的自定义登录API
      console.warn('自定义登录暂时不可用')
      return false
    } catch (error) {
      console.error('自定义登录失败:', error)
      return false
    }
  },

  // 登出
  async signOut() {
    try {
      await auth.signOut()
      return true
    } catch (error) {
      console.error('登出失败:', error)
      return false
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      const loginState = await auth.getLoginState()
      return loginState?.user || null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }
}

// 数据库操作
export const dbService = {
  // 通用查询
  async query(collection: string, where?: any, orderBy?: any, limit?: number) {
    try {
      let query: any = db.collection(collection)

      if (where) {
        query = query.where(where)
      }

      if (orderBy) {
        query = query.orderBy(orderBy.field, orderBy.direction)
      }

      if (limit) {
        query = query.limit(limit)
      }

      const result = await query.get()
      return result.data
    } catch (error) {
      console.error(`查询${collection}失败:`, error)
      return []
    }
  },

  // 添加文档
  async add(collection: string, data: any) {
    try {
      const result = await db.collection(collection).add(data)
      return (result as any).id
    } catch (error) {
      console.error(`添加${collection}文档失败:`, error)
      return null
    }
  },

  // 更新文档
  async update(collection: string, id: string, data: any) {
    try {
      await db.collection(collection).doc(id).update(data)
      return true
    } catch (error) {
      console.error(`更新${collection}文档失败:`, error)
      return false
    }
  },

  // 删除文档
  async delete(collection: string, id: string) {
    try {
      await db.collection(collection).doc(id).remove()
      return true
    } catch (error) {
      console.error(`删除${collection}文档失败:`, error)
      return false
    }
  },

  // 获取单个文档
  async getDoc(collection: string, id: string) {
    try {
      const result = await db.collection(collection).doc(id).get()
      return result.data[0] || null
    } catch (error) {
      console.error(`获取${collection}文档失败:`, error)
      return null
    }
  }
}

// 存储服务（暂时禁用）
export const storageService = {
  // TODO: 实现存储服务
}

// 云函数调用
export const callFunction = async (name: string, data?: any) => {
  try {
    // 确保已经登录（匿名登录）
    const loginState = await auth.getLoginState()
    if (!loginState || !loginState.user) {
      await auth.signInAnonymously()
    }

    const result = await app.callFunction({
      name,
      data
    })
    return result.result
  } catch (error) {
    console.error(`调用云函数${name}失败:`, error)
    return null
  }
}

export { app, auth, db }
export default app
