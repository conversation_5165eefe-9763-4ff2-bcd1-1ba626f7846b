<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/cloud-notes-icon.svg" />
    <link rel="apple-touch-icon" href="/icon-192.svg" />
    <link rel="icon" sizes="192x192" href="/icon-192.svg" />
    <link rel="icon" sizes="512x512" href="/icon-512.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="云笔记 - 基于CloudBase的现代化AI云笔记管理系统，支持双主题切换" />
    <meta name="keywords" content="云笔记,AI,CloudBase,笔记管理,双主题,Markdown" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#58a6ff" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="云笔记" />
    <title>云笔记</title>
    <style>
      /* 预加载样式 */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #0d1117;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-content {
        text-align: center;
        color: #f0f6fc;
      }

      .loading-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #58a6ff;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(88, 166, 255, 0.3);
        border-top: 3px solid #58a6ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="loading-screen">
      <div class="loading-content">
        <h1 class="loading-title">云笔记</h1>
        <div class="loading-spinner"></div>
      </div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // 隐藏加载屏幕
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
