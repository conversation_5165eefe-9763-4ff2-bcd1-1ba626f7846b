/* 通用组件样式 - 优化深色主题体验 */

/* 输入框基础样式 */
.input-base {
  @apply w-full px-3 py-2 bg-theme-bg border border-theme-border rounded-lg;
  @apply text-theme-text placeholder-theme-text-muted;
  @apply font-theme-normal leading-theme-normal;
  @apply transition-all duration-200 ease-out;
  @apply focus:outline-none focus:border-theme-primary focus:ring-1 focus:ring-theme-primary;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.input-base:hover:not(:disabled) {
  @apply border-theme-border-subtle;
}

.input-base:focus {
  @apply shadow-theme-sm;
}

/* 输入框尺寸变体 */
.input-sm {
  @apply px-2 py-1.5 text-sm;
}

.input-lg {
  @apply px-4 py-3 text-base;
}

/* 按钮基础样式 */
.btn-base {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg;
  @apply font-theme-medium leading-theme-tight;
  @apply transition-all duration-200 ease-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply active:scale-95 transform;
}

/* 按钮变体 */
.btn-primary {
  @apply bg-theme-primary hover:bg-theme-primary-hover text-white;
  @apply focus:ring-theme-primary focus:ring-offset-theme-bg;
  @apply shadow-theme-sm hover:shadow-theme;
}

.btn-secondary {
  @apply bg-theme-surface hover:bg-theme-surface-hover;
  @apply text-theme-text border border-theme-border hover:border-theme-border-subtle;
  @apply focus:ring-theme-primary focus:ring-offset-theme-bg;
  @apply shadow-theme-sm hover:shadow-theme;
}

.btn-ghost {
  @apply bg-transparent hover:bg-theme-surface;
  @apply text-theme-text-muted hover:text-theme-text;
  @apply focus:ring-theme-primary focus:ring-offset-theme-bg;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white;
  @apply focus:ring-red-500 focus:ring-offset-theme-bg;
  @apply shadow-theme-sm hover:shadow-theme;
}

/* 按钮尺寸 */
.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

/* 卡片样式 */
.card-base {
  @apply bg-theme-surface border border-theme-border rounded-lg;
  @apply shadow-theme-sm;
}

.card-hover {
  @apply hover:shadow-theme hover:border-theme-border-subtle;
  @apply transition-all duration-200 ease-out;
}

/* 分割线 */
.divider {
  @apply border-theme-border-muted;
}

/* 文本样式 */
.text-heading {
  @apply font-theme-bold text-theme-text leading-theme-tight;
}

.text-body {
  @apply font-theme-normal text-theme-text leading-theme-normal;
}

.text-caption {
  @apply font-theme-normal text-theme-text-muted text-sm leading-theme-normal;
}

.text-label {
  @apply font-theme-medium text-theme-text text-sm leading-theme-tight;
}

/* 链接样式 */
.link-base {
  @apply text-theme-primary hover:text-theme-primary-hover;
  @apply font-theme-normal underline-offset-2;
  @apply transition-colors duration-200 ease-out;
  @apply focus:outline-none focus:underline;
}

/* 表单组样式 */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-label mb-1;
}

.form-error {
  @apply text-red-500 text-sm font-theme-normal mt-1;
}

.form-help {
  @apply text-caption mt-1;
}

/* 加载状态 */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-theme-border;
  @apply border-t-theme-primary;
}

/* 工具提示 */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs font-theme-normal;
  @apply bg-theme-surface border border-theme-border rounded;
  @apply text-theme-text shadow-theme-md;
  @apply opacity-0 invisible transition-all duration-200;
}

.tooltip.show {
  @apply opacity-100 visible;
}

/* 下拉菜单 */
.dropdown-menu {
  @apply absolute z-50 mt-1 bg-theme-surface border border-theme-border rounded-lg;
  @apply shadow-theme-lg min-w-48 py-1;
  @apply opacity-0 invisible scale-95 transition-all duration-200 ease-out;
}

.dropdown-menu.show {
  @apply opacity-100 visible scale-100;
}

.dropdown-item {
  @apply block w-full px-3 py-2 text-left text-sm font-theme-normal;
  @apply text-theme-text hover:bg-theme-surface-hover;
  @apply transition-colors duration-150 ease-out;
  @apply focus:outline-none focus:bg-theme-surface-hover;
}

/* 模态框 */
.modal-overlay {
  @apply fixed inset-0 z-50 bg-black bg-opacity-50;
  @apply flex items-center justify-center p-4;
  @apply opacity-0 invisible transition-all duration-300 ease-out;
}

.modal-overlay.show {
  @apply opacity-100 visible;
}

.modal-content {
  @apply bg-theme-surface border border-theme-border rounded-lg;
  @apply shadow-xl max-w-md w-full;
  @apply transform scale-95 transition-all duration-300 ease-out;
}

.modal-overlay.show .modal-content {
  @apply scale-100;
}

/* 通知样式 */
.notification {
  @apply px-4 py-3 rounded-lg border font-theme-normal;
  @apply transition-all duration-300 ease-out;
}

.notification.success {
  @apply bg-green-50 border-green-200 text-green-800;
}

.notification.error {
  @apply bg-red-50 border-red-200 text-red-800;
}

.notification.warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.notification.info {
  @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* 深色主题下的通知样式 */
[data-theme="dark"] .notification.success {
  @apply bg-green-900/20 border-green-700/30 text-green-300;
}

[data-theme="dark"] .notification.error {
  @apply bg-red-900/20 border-red-700/30 text-red-300;
}

[data-theme="dark"] .notification.warning {
  @apply bg-yellow-900/20 border-yellow-700/30 text-yellow-300;
}

[data-theme="dark"] .notification.info {
  @apply bg-blue-900/20 border-blue-700/30 text-blue-300;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--theme-border) var(--theme-bg-secondary);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--theme-bg-secondary);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--theme-border-subtle);
}

/* 选择文本样式 */
::selection {
  background: var(--theme-primary);
  color: white;
}

::-moz-selection {
  background: var(--theme-primary);
  color: white;
}
