import React, { useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FolderPlusIcon,
  HtmlIcon,
  DocumentIcon,
  EditIcon,
  CopyIcon,
  MoveIcon,
  RenameIcon,
  EyeIcon,
  OpenIcon,
  DownloadIcon,
  ShareIcon,
  TrashIcon
} from '../Icons/IconLibrary'

export interface ContextMenuItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  onClick: () => void
  disabled?: boolean
}

interface ContextMenuProps {
  isVisible: boolean
  position: { x: number; y: number }
  items: ContextMenuItem[]
  onClose: () => void
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  isVisible,
  position,
  items,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isVisible, onClose])

  // 调整菜单位置以防止超出视窗
  const adjustPosition = (x: number, y: number) => {
    if (!menuRef.current) return { x, y }

    const menuRect = menuRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let adjustedX = x
    let adjustedY = y

    // 防止右侧超出
    if (x + menuRect.width > viewportWidth) {
      adjustedX = viewportWidth - menuRect.width - 10
    }

    // 防止底部超出
    if (y + menuRect.height > viewportHeight) {
      adjustedY = viewportHeight - menuRect.height - 10
    }

    // 防止左侧超出
    if (adjustedX < 10) {
      adjustedX = 10
    }

    // 防止顶部超出
    if (adjustedY < 10) {
      adjustedY = 10
    }

    return { x: adjustedX, y: adjustedY }
  }

  const adjustedPosition = adjustPosition(position.x, position.y)

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* 背景遮罩 */}
          <div className="fixed inset-0 z-40" />
          
          {/* 菜单内容 */}
          <motion.div
            ref={menuRef}
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.15 }}
            className="fixed z-50 min-w-48 bg-theme-surface border border-theme-border/50 rounded-lg shadow-xl backdrop-blur-sm"
            style={{
              left: adjustedPosition.x,
              top: adjustedPosition.y,
            }}
          >
            <div className="py-2">
              {items.map((item, index) => {
                const Icon = item.icon

                // 渲染分隔符
                if (item.label === '---') {
                  return (
                    <div
                      key={item.id}
                      className="my-1 mx-2 border-t border-theme-border/30"
                    />
                  )
                }

                return (
                  <motion.button
                    key={item.id}
                    className={`
                      w-full px-4 py-2.5 text-left text-sm font-medium
                      flex items-center gap-3 transition-all duration-150
                      ${item.disabled
                        ? 'text-theme-text-muted cursor-not-allowed opacity-50'
                        : 'text-theme-text hover:bg-theme-primary/10 hover:text-theme-primary active:bg-theme-primary/20'
                      }
                      ${index === 0 ? 'rounded-t-lg' : ''}
                      ${index === items.length - 1 ? 'rounded-b-lg' : ''}
                    `}
                    onClick={() => {
                      if (!item.disabled) {
                        item.onClick()
                        onClose()
                      }
                    }}
                    disabled={item.disabled}
                    whileHover={!item.disabled ? { x: 2 } : {}}
                    whileTap={!item.disabled ? { scale: 0.98 } : {}}
                  >
                    <Icon className={`
                      icon-sm flex-shrink-0
                      ${item.disabled ? 'text-theme-text-muted' : 'text-current'}
                    `} />
                    <span className="flex-1">{item.label}</span>
                  </motion.button>
                )
              })}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

// 预定义的菜单项创建函数 - 空白区域右键菜单
export const createDirectoryContextMenuItems = (
  onCreateFolder: () => void,
  onCreateNote: () => void,
  onCreateHtml: () => void
): ContextMenuItem[] => [
  {
    id: 'create-folder',
    label: '新建文件夹',
    icon: FolderPlusIcon,
    onClick: onCreateFolder
  },
  {
    id: 'create-note',
    label: '新建Markdown笔记',
    icon: DocumentIcon,
    onClick: onCreateNote
  },
  {
    id: 'create-html',
    label: '新建HTML文件',
    icon: HtmlIcon,
    onClick: onCreateHtml
  }
]

// 文件夹右键菜单
export const createFolderContextMenuItems = (
  onOpen: () => void,
  onRename: () => void,
  onCreateFolder: () => void,
  onCreateNote: () => void,
  onCreateHtml: () => void,
  onCopy: () => void,
  onMove: () => void,
  onShare: () => void,
  onDelete: () => void
): ContextMenuItem[] => [
  {
    id: 'open-folder',
    label: '打开',
    icon: OpenIcon,
    onClick: onOpen
  },
  {
    id: 'rename-folder',
    label: '重命名',
    icon: RenameIcon,
    onClick: onRename
  },
  {
    id: 'separator-1',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'create-folder',
    label: '新建文件夹',
    icon: FolderPlusIcon,
    onClick: onCreateFolder
  },
  {
    id: 'create-note',
    label: '新建Markdown笔记',
    icon: DocumentIcon,
    onClick: onCreateNote
  },
  {
    id: 'create-html',
    label: '新建HTML文件',
    icon: HtmlIcon,
    onClick: onCreateHtml
  },
  {
    id: 'separator-2',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'copy-folder',
    label: '复制',
    icon: CopyIcon,
    onClick: onCopy
  },
  {
    id: 'move-folder',
    label: '移动',
    icon: MoveIcon,
    onClick: onMove
  },
  {
    id: 'share-folder',
    label: '分享',
    icon: ShareIcon,
    onClick: onShare
  },
  {
    id: 'separator-3',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'delete-folder',
    label: '移到回收站',
    icon: TrashIcon,
    onClick: onDelete
  }
]

// Markdown文件右键菜单
export const createNoteContextMenuItems = (
  onOpen: () => void,
  onEdit: () => void,
  onPreview: () => void,
  onRename: () => void,
  onCopy: () => void,
  onMove: () => void,
  onDownload: () => void,
  onShare: () => void,
  onDelete: () => void
): ContextMenuItem[] => [
  {
    id: 'open-note',
    label: '打开',
    icon: OpenIcon,
    onClick: onOpen
  },
  {
    id: 'edit-note',
    label: '编辑',
    icon: EditIcon,
    onClick: onEdit
  },
  {
    id: 'preview-note',
    label: '预览',
    icon: EyeIcon,
    onClick: onPreview
  },
  {
    id: 'rename-note',
    label: '重命名',
    icon: RenameIcon,
    onClick: onRename
  },
  {
    id: 'separator-1',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'copy-note',
    label: '复制',
    icon: CopyIcon,
    onClick: onCopy
  },
  {
    id: 'move-note',
    label: '移动',
    icon: MoveIcon,
    onClick: onMove
  },
  {
    id: 'download-note',
    label: '下载',
    icon: DownloadIcon,
    onClick: onDownload
  },
  {
    id: 'share-note',
    label: '分享',
    icon: ShareIcon,
    onClick: onShare
  },
  {
    id: 'separator-2',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'delete-note',
    label: '移到回收站',
    icon: TrashIcon,
    onClick: onDelete
  }
]

// HTML文件右键菜单
export const createHtmlContextMenuItems = (
  onOpen: () => void,
  onEdit: () => void,
  onPreview: () => void,
  onRename: () => void,
  onCopy: () => void,
  onMove: () => void,
  onDownload: () => void,
  onShare: () => void,
  onDelete: () => void
): ContextMenuItem[] => [
  {
    id: 'open-html',
    label: '打开',
    icon: OpenIcon,
    onClick: onOpen
  },
  {
    id: 'edit-html',
    label: '编辑',
    icon: EditIcon,
    onClick: onEdit
  },
  {
    id: 'preview-html',
    label: '预览',
    icon: EyeIcon,
    onClick: onPreview
  },
  {
    id: 'rename-html',
    label: '重命名',
    icon: RenameIcon,
    onClick: onRename
  },
  {
    id: 'separator-1',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'copy-html',
    label: '复制',
    icon: CopyIcon,
    onClick: onCopy
  },
  {
    id: 'move-html',
    label: '移动',
    icon: MoveIcon,
    onClick: onMove
  },
  {
    id: 'download-html',
    label: '下载',
    icon: DownloadIcon,
    onClick: onDownload
  },
  {
    id: 'share-html',
    label: '分享',
    icon: ShareIcon,
    onClick: onShare
  },
  {
    id: 'separator-2',
    label: '---',
    icon: () => null,
    onClick: () => {}
  },
  {
    id: 'delete-html',
    label: '移到回收站',
    icon: TrashIcon,
    onClick: onDelete
  }
]

export default ContextMenu
