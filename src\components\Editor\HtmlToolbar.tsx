import React from 'react'
import { motion } from 'framer-motion'
import {
  CodeIcon,
  TagIcon,
  ImageIcon,
  LinkIcon,
  TableIcon,
  ListIcon,
  PlayIcon
} from '../Icons/IconLibrary'

interface HtmlToolbarProps {
  onInsertText: (text: string, cursorOffset?: number) => void
  onWrapText: (prefix: string, suffix: string) => void
  disabled?: boolean
}

const HtmlToolbar: React.FC<HtmlToolbarProps> = ({
  onInsertText,
  onWrapText,
  disabled = false
}) => {
  const htmlToolbarItems = [
    {
      id: 'div',
      icon: TagIcon,
      title: '插入 div 标签',
      action: () => onWrapText('<div>', '</div>')
    },
    {
      id: 'span',
      icon: TagIcon,
      title: '插入 span 标签',
      action: () => onWrapText('<span>', '</span>')
    },
    {
      id: 'p',
      icon: TagIcon,
      title: '插入段落标签',
      action: () => onWrapText('<p>', '</p>')
    },
    { id: 'divider1', type: 'divider' },
    {
      id: 'h1',
      icon: TagIcon,
      title: '插入 H1 标题',
      action: () => onWrapText('<h1>', '</h1>')
    },
    {
      id: 'h2',
      icon: TagIcon,
      title: '插入 H2 标题',
      action: () => onWrapText('<h2>', '</h2>')
    },
    {
      id: 'h3',
      icon: TagIcon,
      title: '插入 H3 标题',
      action: () => onWrapText('<h3>', '</h3>')
    },
    { id: 'divider2', type: 'divider' },
    {
      id: 'a',
      icon: LinkIcon,
      title: '插入链接',
      action: () => onInsertText('<a href="https://example.com">链接文本</a>', -4)
    },
    {
      id: 'img',
      icon: ImageIcon,
      title: '插入图片',
      action: () => onInsertText('<img src="https://example.com/image.jpg" alt="图片描述" />', 0)
    },
    { id: 'divider3', type: 'divider' },
    {
      id: 'ul',
      icon: ListIcon,
      title: '插入无序列表',
      action: () => onInsertText('<ul>\n  <li>列表项1</li>\n  <li>列表项2</li>\n</ul>', 0)
    },
    {
      id: 'ol',
      icon: ListIcon,
      title: '插入有序列表',
      action: () => onInsertText('<ol>\n  <li>列表项1</li>\n  <li>列表项2</li>\n</ol>', 0)
    },
    {
      id: 'table',
      icon: TableIcon,
      title: '插入表格',
      action: () => onInsertText(`<table>
  <thead>
    <tr>
      <th>列1</th>
      <th>列2</th>
      <th>列3</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>内容1</td>
      <td>内容2</td>
      <td>内容3</td>
    </tr>
  </tbody>
</table>`, 0)
    },
    { id: 'divider4', type: 'divider' },
    {
      id: 'script',
      icon: CodeIcon,
      title: '插入 JavaScript',
      action: () => onInsertText('<script>\n  // JavaScript 代码\n  \n</script>', -11)
    },
    {
      id: 'style',
      icon: CodeIcon,
      title: '插入 CSS 样式',
      action: () => onInsertText('<style>\n  /* CSS 样式 */\n  \n</style>', -10)
    },
    { id: 'divider5', type: 'divider' },
    {
      id: 'template',
      icon: PlayIcon,
      title: '插入基础HTML模板',
      action: () => onInsertText(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎使用HTML编辑器</h1>
        <p>在这里开始您的HTML创作...</p>
    </div>
</body>
</html>`, 0)
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center gap-1 p-2 bg-theme-surface border-b border-theme-border"
    >
      {htmlToolbarItems.map((item) => {
        if (item.type === 'divider') {
          return (
            <div
              key={item.id}
              className="w-px h-6 bg-theme-border mx-1"
            />
          )
        }

        const Icon = item.icon!
        
        return (
          <motion.button
            key={item.id}
            onClick={item.action}
            disabled={disabled}
            title={item.title}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`
              flex items-center justify-center w-8 h-8 rounded-md
              transition-all duration-200 ease-out
              ${disabled 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:bg-theme-surface-hover active:bg-theme-surface-active'
              }
              text-theme-text-muted hover:text-theme-text
              focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-1 focus:ring-offset-theme-surface
            `}
          >
            <Icon className="icon-sm" />
          </motion.button>
        )
      })}
      
      {/* HTML快捷键提示 */}
      <div className="ml-auto flex items-center gap-2 text-xs text-theme-text-subtle">
        <span className="hidden sm:block">
          HTML编辑模式 | Ctrl+S 保存 | 使用预览查看效果
        </span>
      </div>
    </motion.div>
  )
}

export default HtmlToolbar
