import React from 'react'
import { motion } from 'framer-motion'
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  CodeIcon,
  ListIcon,
  QuoteIcon,
  ImageIcon,
  TableIcon,
  HeadingIcon,
  StrikethroughIcon
} from '../Icons/IconLibrary'

interface EditorToolbarProps {
  onInsertText: (text: string, cursorOffset?: number) => void
  onWrapText: (prefix: string, suffix: string) => void
  disabled?: boolean
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  onInsertText,
  onWrapText,
  disabled = false
}) => {
  const toolbarItems = [
    {
      id: 'bold',
      icon: BoldIcon,
      title: '加粗 (Ctrl+B)',
      action: () => onWrapText('**', '**'),
      shortcut: 'Ctrl+B'
    },
    {
      id: 'italic',
      icon: ItalicIcon,
      title: '斜体 (Ctrl+I)',
      action: () => onWrapText('*', '*'),
      shortcut: 'Ctrl+I'
    },
    {
      id: 'strikethrough',
      icon: StrikethroughIcon,
      title: '删除线',
      action: () => onWrapText('~~', '~~')
    },
    { id: 'divider1', type: 'divider' },
    {
      id: 'heading',
      icon: HeadingIcon,
      title: '标题',
      action: () => onInsertText('## ', 0)
    },
    {
      id: 'quote',
      icon: QuoteIcon,
      title: '引用',
      action: () => onInsertText('> ', 0)
    },
    {
      id: 'code',
      icon: CodeIcon,
      title: '代码',
      action: () => onWrapText('`', '`')
    },
    {
      id: 'code-block',
      icon: CodeIcon,
      title: '代码块',
      action: () => onInsertText('\n```\n\n```\n', -5)
    },
    { id: 'divider2', type: 'divider' },
    {
      id: 'link',
      icon: LinkIcon,
      title: '链接 (Ctrl+K)',
      action: () => onInsertText('[链接文本](https://example.com)', -21),
      shortcut: 'Ctrl+K'
    },
    {
      id: 'image',
      icon: ImageIcon,
      title: '图片',
      action: () => onInsertText('![图片描述](https://example.com/image.jpg)', -32)
    },
    { id: 'divider3', type: 'divider' },
    {
      id: 'list',
      icon: ListIcon,
      title: '无序列表',
      action: () => onInsertText('- ', 0)
    },
    {
      id: 'ordered-list',
      icon: ListIcon,
      title: '有序列表',
      action: () => onInsertText('1. ', 0)
    },
    {
      id: 'table',
      icon: TableIcon,
      title: '表格',
      action: () => onInsertText('\n| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容 | 内容 | 内容 |\n', 0)
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center gap-1 p-2 bg-theme-surface border-b border-theme-border"
    >
      {toolbarItems.map((item) => {
        if (item.type === 'divider') {
          return (
            <div
              key={item.id}
              className="w-px h-6 bg-theme-border mx-1"
            />
          )
        }

        const Icon = item.icon!
        
        return (
          <motion.button
            key={item.id}
            onClick={item.action}
            disabled={disabled}
            title={item.title}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`
              flex items-center justify-center w-8 h-8 rounded-md
              transition-all duration-200 ease-out
              ${disabled 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:bg-theme-surface-hover active:bg-theme-surface-active'
              }
              text-theme-text-muted hover:text-theme-text
              focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-1 focus:ring-offset-theme-surface
            `}
          >
            <Icon className="icon-sm" />
          </motion.button>
        )
      })}
      
      {/* 快捷键提示 */}
      <div className="ml-auto flex items-center gap-2 text-xs text-theme-text-subtle">
        <span className="hidden sm:block">
          快捷键: Ctrl+S 保存, Ctrl+B 加粗, Ctrl+I 斜体
        </span>
      </div>
    </motion.div>
  )
}

export default EditorToolbar
