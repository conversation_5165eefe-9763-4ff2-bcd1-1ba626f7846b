import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

// 组件导入（暂时使用占位符）
import LoginPage from './pages/LoginPage'
import Dashboard from './pages/Dashboard'
import AdminPanel from './pages/AdminPanel'
import ShareView from './pages/ShareView'

// 状态管理
import { useAuthStore } from './stores/authStore'
import { useThemeStore } from './stores/themeStore'

// 主题组件
import ThemeEffectsManager from './components/Theme/ThemeEffectsManager'

// 样式
import './App.css'

function App() {
  const { isAuthenticated, isLoading, checkAuth, setLoading } = useAuthStore()
  const { applyTheme } = useThemeStore()

  useEffect(() => {
    // 应用启动时先进行匿名登录，然后检查认证状态
    const initAuth = async () => {
      try {
        const { authService } = await import('./utils/cloudbase')
        await authService.signInAnonymously()
        await checkAuth()
      } catch (error) {
        console.error('初始化认证失败:', error)
        setLoading(false)
      }
    }

    initAuth()
  }, [])

  // 初始化太阳系主题
  useEffect(() => {
    applyTheme('solar')
  }, [applyTheme])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-theme-background flex items-center justify-center theme-transition">
        <div className="solar-loader">
          <div className="sun animate-solar-pulse bg-theme-primary"></div>
          <div className="orbit orbit-1 animate-orbit border-theme-secondary/30"></div>
          <div className="orbit orbit-2 animate-orbit border-theme-accent/30"></div>
          <div className="orbit orbit-3 animate-orbit border-theme-primary/30"></div>
        </div>
        <ThemeEffectsManager />
      </div>
    )
  }

  return (
    <div className="App theme-transition" data-theme="solar">
      {/* 太阳系主题效果管理器 */}
      <ThemeEffectsManager />

      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/share/:shareId" element={<ShareView />} />

        {/* 受保护的路由 */}
        <Route
          path="/dashboard/*"
          element={
            isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />
          }
        />
        <Route
          path="/admin/*"
          element={
            isAuthenticated ? <AdminPanel /> : <Navigate to="/login" replace />
          }
        />

        {/* 默认重定向 */}
        <Route
          path="/"
          element={
            <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
          }
        />

        {/* 404页面 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* 全局通知 */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        toastClassName="bg-theme-surface border border-theme-primary/20"
      />
    </div>
  )
}

export default App
