import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  UserIcon,
  LogOutIcon,
  CloudNotesIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '../Icons/IconLibrary'
import { useAuthStore } from '../../stores/authStore'
import { useNotesStore } from '../../stores/notesStore'
import { useDirectoryStore } from '../../stores/directoryStore'
import ThemeToggle from '../ThemeToggle/ThemeToggle'
import DirectoryTree from '../DirectoryTree/DirectoryTree'
import DirectorySearchBox from '../DirectoryTree/DirectorySearchBox'
import NoteTabs from '../Editor/NoteTabs'
import EditorTabs from '../Editor/EditorTabs'
import { useEditorMode, EditorModeProvider } from '../../contexts/EditorModeContext'


interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayoutContent: React.FC<MainLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const { editorMode, setEditorMode } = useEditorMode()
  const { user, logout } = useAuthStore()
  const { activeNoteId, setActiveNote, openTabs, getActiveTab } = useNotesStore()

  const activeTab = getActiveTab()
  const hasUnsavedChanges = activeTab?.hasUnsavedChanges || false

  // 判断当前文件类型
  const getCurrentFileType = (): 'markdown' | 'html' => {
    if (!activeTab) return 'markdown'
    const extension = activeTab.title.split('.').pop()?.toLowerCase()
    return extension === 'html' || extension === 'htm' ? 'html' : 'markdown'
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  const { openDirectoryItemInTab } = useNotesStore()
  const { items } = useDirectoryStore()

  const handleSelectNote = (noteId: string) => {
    setActiveNote(noteId)

    // 从directoryStore中查找对应的项目
    const findItemById = (items: any[], id: string): any => {
      for (const item of items) {
        if (item._id === id) {
          return item
        }
        if (item.children) {
          const found = findItemById(item.children, id)
          if (found) return found
        }
      }
      return null
    }

    const selectedItem = findItemById(items, noteId)
    if (selectedItem) {
      openDirectoryItemInTab(selectedItem)
    }
  }

  return (
    <div className="h-screen bg-theme-bg flex flex-col">
      {/* 顶部导航栏 */}
      <header className="h-14 bg-theme-surface/80 backdrop-blur-lg border-b border-theme-border flex items-center justify-between px-4 z-10">
        {/* 左侧 - 应用标题和菜单 */}
        <div className="flex items-center gap-3">
          <button
            className="p-2 rounded-lg hover:bg-theme-surface-hover transition-colors"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            title={sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'}
          >
            {sidebarCollapsed ? (
              <ChevronRightIcon className="nav-icon" />
            ) : (
              <ChevronLeftIcon className="nav-icon" />
            )}
          </button>
          
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-theme-primary/10 border border-theme-primary/20">
              <CloudNotesIcon className="icon-primary icon-lg" />
            </div>
            <h1 className="text-lg font-theme-bold text-theme-text hidden sm:block">
              云笔记
            </h1>
          </div>
        </div>

        {/* 中间区域 - 留空 */}
        <div className="flex-1"></div>

        {/* 右侧 - 用户信息和设置 */}
        <div className="flex items-center gap-2">
          <ThemeToggle />

          <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-theme-surface border border-theme-border">
            <UserIcon className="icon-primary icon-md" />
            <span className="text-sm font-theme-normal text-theme-text hidden sm:block">
              {user?.username}
            </span>
          </div>

          <button
            onClick={handleLogout}
            className="p-2 rounded-lg hover:bg-theme-surface-hover transition-colors"
            title="退出登录"
          >
            <LogOutIcon className="action-icon" />
          </button>
        </div>
      </header>

      {/* 标签页区域 - 显示在导航栏下方 */}
      <div className="h-10 bg-theme-surface/80 backdrop-blur-lg border-b border-theme-border flex items-center">
        <motion.div
          initial={false}
          animate={{
            width: sidebarCollapsed ? 0 : 280,
            opacity: sidebarCollapsed ? 0 : 1
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="flex-shrink-0 bg-theme-bg-secondary border-r border-theme-border overflow-hidden"
        >
          {/* 搜索框 - 位于左侧边栏顶部 */}
          <div className="h-full flex items-center px-2">
            <DirectorySearchBox
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="搜索笔记..."
              className="w-full"
            />
          </div>
        </motion.div>

        {/* 标签页内容区域 */}
        <div className="flex-1 h-10 flex items-center">
          {openTabs.length > 0 && (
            <>
              <div className="flex-1">
                <NoteTabs className="h-full" />
              </div>

              {/* 编辑器模式切换标签页 */}
              <div className="flex-shrink-0 h-full flex items-center px-2">
                <EditorTabs
                  activeMode={editorMode}
                  onModeChange={setEditorMode}
                  hasUnsavedChanges={hasUnsavedChanges}
                  disabled={false}
                  fileType={getCurrentFileType()}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧目录树 */}
        <motion.aside
          initial={false}
          animate={{
            width: sidebarCollapsed ? 0 : 280,
            opacity: sidebarCollapsed ? 0 : 1
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="bg-theme-bg-secondary border-r border-theme-border overflow-hidden"
        >
          {/* 目录树内容 - 搜索框已移动到标签页区域 */}
          <DirectoryTree
            onSelectNote={handleSelectNote}
            selectedNoteId={activeNoteId || undefined}
            searchQuery={searchQuery}
          />
        </motion.aside>

        {/* 右侧主要内容区域 */}
        <main className="flex-1 flex flex-col overflow-hidden">
          {children}
        </main>
      </div>


    </div>
  )
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <EditorModeProvider>
      <MainLayoutContent>{children}</MainLayoutContent>
    </EditorModeProvider>
  )
}

export default MainLayout
