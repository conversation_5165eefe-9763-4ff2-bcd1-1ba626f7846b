import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Bell, X, Check, Info, AlertTriangle, Zap } from 'lucide-react'

interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

interface NotificationCenterProps {
  className?: string
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'info',
      title: '欢迎来到太阳系',
      message: '您的知识星系已准备就绪，开始探索吧！',
      timestamp: new Date().toISOString(),
      read: false
    },
    {
      id: '2',
      type: 'success',
      title: '云函数部署成功',
      message: '认证和笔记管理功能已上线',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      read: false
    },
    {
      id: '3',
      type: 'warning',
      title: '功能开发中',
      message: '部分高级功能仍在开发中，敬请期待',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      read: true
    }
  ])

  const unreadCount = notifications.filter(n => !n.read).length

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Check className="w-5 h-5 text-earth-green" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-jupiter-storm" />
      case 'error':
        return <X className="w-5 h-5 text-mars-red" />
      default:
        return <Info className="w-5 h-5 text-earth-blue" />
    }
  }

  const getNotificationBg = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-earth-green/10 border-earth-green/20'
      case 'warning':
        return 'bg-jupiter-storm/10 border-jupiter-storm/20'
      case 'error':
        return 'bg-mars-red/10 border-mars-red/20'
      default:
        return 'bg-earth-blue/10 border-earth-blue/20'
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, read: true }))
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const formatTime = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now.getTime() - time.getTime()
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    return time.toLocaleDateString()
  }

  return (
    <div className={`relative ${className}`}>
      {/* 通知按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 rounded-full hover:bg-sun-corona/10 transition-colors"
      >
        <Bell className="w-5 h-5 text-venus-cloud" />
        
        {/* 未读数量徽章 */}
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 w-5 h-5 bg-mars-red rounded-full flex items-center justify-center"
          >
            <span className="text-xs text-white font-bold">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          </motion.div>
        )}
      </button>

      {/* 通知面板 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* 通知列表 */}
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              className="absolute top-full right-0 mt-2 w-96 bg-starfield/95 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl shadow-cosmic z-50"
            >
              {/* 头部 */}
              <div className="flex items-center justify-between p-4 border-b border-sun-corona/20">
                <h3 className="text-lg font-semibold text-venus-cloud flex items-center gap-2">
                  <Zap className="w-5 h-5 text-sun-corona" />
                  星系通知
                </h3>
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-sun-corona hover:text-sun-corona/80 transition-colors"
                  >
                    全部已读
                  </button>
                )}
              </div>

              {/* 通知列表 */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-venus-cloud/60">
                    <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>暂无通知</p>
                    <p className="text-sm mt-1">太阳系一切正常运行</p>
                  </div>
                ) : (
                  <div className="p-2">
                    {notifications.map((notification, index) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`p-3 mb-2 rounded-lg border transition-all duration-300 ${
                          notification.read 
                            ? 'bg-cosmic-void/30 border-sun-corona/10 opacity-70' 
                            : getNotificationBg(notification.type)
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className="mt-0.5">
                            {getNotificationIcon(notification.type)}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <h4 className="text-venus-cloud font-medium text-sm">
                                {notification.title}
                              </h4>
                              <button
                                onClick={() => removeNotification(notification.id)}
                                className="text-venus-cloud/40 hover:text-venus-cloud/80 transition-colors ml-2"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                            
                            <p className="text-venus-cloud/80 text-sm mt-1">
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-venus-cloud/50">
                                {formatTime(notification.timestamp)}
                              </span>
                              
                              {!notification.read && (
                                <button
                                  onClick={() => markAsRead(notification.id)}
                                  className="text-xs text-sun-corona hover:text-sun-corona/80 transition-colors"
                                >
                                  标记已读
                                </button>
                              )}
                            </div>
                            
                            {notification.action && (
                              <button
                                onClick={notification.action.onClick}
                                className="mt-2 text-sm text-sun-corona hover:text-sun-corona/80 transition-colors"
                              >
                                {notification.action.label}
                              </button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

export default NotificationCenter
