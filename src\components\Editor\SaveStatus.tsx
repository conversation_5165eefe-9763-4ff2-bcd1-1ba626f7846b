import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckIcon, LoaderIcon, AlertTriangleIcon, SaveIcon } from '../Icons/IconLibrary'

export type SaveStatus = 'saved' | 'unsaved' | 'saving' | 'error'

interface SaveStatusProps {
  status: SaveStatus
  lastSavedTime?: Date
  errorMessage?: string
  className?: string
}

const SaveStatus: React.FC<SaveStatusProps> = ({
  status,
  lastSavedTime,
  errorMessage,
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'saved':
        return {
          icon: CheckIcon,
          text: '已保存',
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/20'
        }
      case 'unsaved':
        return {
          icon: SaveIcon,
          text: '未保存',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500/20'
        }
      case 'saving':
        return {
          icon: LoaderIcon,
          text: '保存中...',
          color: 'text-blue-500',
          bgColor: 'bg-blue-500/10',
          borderColor: 'border-blue-500/20'
        }
      case 'error':
        return {
          icon: AlertTriangleIcon,
          text: '保存失败',
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/20'
        }
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon

  const formatLastSavedTime = (time: Date) => {
    const now = new Date()
    const diff = now.getTime() - time.getTime()
    const minutes = Math.floor(diff / 60000)
    
    if (minutes < 1) {
      return '刚刚'
    } else if (minutes < 60) {
      return `${minutes}分钟前`
    } else {
      return time.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={status}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
        className={`
          inline-flex items-center gap-2 px-3 py-1.5 rounded-md text-sm
          ${config.bgColor} ${config.borderColor} ${config.color}
          border transition-all duration-200
          ${className}
        `}
        title={
          status === 'error' && errorMessage 
            ? errorMessage 
            : status === 'saved' && lastSavedTime
            ? `最后保存: ${lastSavedTime.toLocaleString('zh-CN')}`
            : undefined
        }
      >
        <Icon 
          className={`
            icon-sm ${config.color}
            ${status === 'saving' ? 'animate-spin' : ''}
          `} 
        />
        
        <span className="font-medium">
          {config.text}
        </span>
        
        {status === 'saved' && lastSavedTime && (
          <span className="text-xs opacity-75">
            {formatLastSavedTime(lastSavedTime)}
          </span>
        )}
        
        {status === 'unsaved' && (
          <motion.div
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1.5 h-1.5 bg-current rounded-full"
          />
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default SaveStatus
