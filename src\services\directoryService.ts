import cloudbase from '@cloudbase/js-sdk'

// 初始化云开发
const app = cloudbase.init({
  env: 'ai-demo-8gjoyg63e237ce06'
})

const auth = app.auth()

// 确保用户已登录
export const ensureAuth = async () => {
  try {
    console.log('Checking authentication state...')

    // 先尝试获取当前登录状态
    let loginState = null
    try {
      loginState = await auth.getLoginState()
      console.log('Current login state:', loginState)
    } catch (error) {
      console.log('Failed to get login state:', error)
    }

    // 如果没有登录状态或没有用户信息，尝试匿名登录
    if (!loginState || !loginState.user) {
      console.log('No valid login state, attempting anonymous login...')
      try {
        const result = await auth.signInAnonymously()
        console.log('Anonymous login result:', result)

        // 再次检查登录状态
        const newLoginState = await auth.getLoginState()
        console.log('New login state after anonymous login:', newLoginState)

        return newLoginState
      } catch (authError) {
        console.error('Anonymous login failed:', authError)
        // 如果匿名登录失败，我们仍然尝试继续，可能环境配置了其他认证方式
        console.log('Continuing without authentication...')
        return null
      }
    }

    return loginState
  } catch (error) {
    console.error('Authentication error:', error)
    // 不抛出错误，允许继续执行
    return null
  }
}

// 目录树项目类型
export interface DirectoryItem {
  _id: string
  name: string
  type: 'folder' | 'note' | 'html'
  parentId: string | null
  path: string
  level: number
  children?: DirectoryItem[]
  content?: string
  isShared?: boolean
  importance?: number
  isExpanded?: boolean
  isDeleted?: boolean
  deletedAt?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 目录树服务类
export class DirectoryService {
  private static instance: DirectoryService
  
  private constructor() {}
  
  static getInstance(): DirectoryService {
    if (!DirectoryService.instance) {
      DirectoryService.instance = new DirectoryService()
    }
    return DirectoryService.instance
  }
  
  /**
   * 获取完整目录树
   */
  async getDirectoryTree(): Promise<DirectoryItem[]> {
    try {
      console.log('Starting getDirectoryTree...')
      await ensureAuth()
      console.log('Authentication successful, calling cloud function...')

      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'getTree'
        }
      })

      console.log('Cloud function result:', result)

      if (result.result && result.result.success) {
        console.log('Directory tree loaded successfully:', result.result.data)
        return result.result.data
      } else {
        const errorMsg = result.result?.error || 'Failed to get directory tree'
        console.error('Cloud function returned error:', errorMsg)
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('Error getting directory tree:', error)
      throw error
    }
  }

  /**
   * 获取单个文件/文件夹的详细信息
   */
  async getItem(id: string): Promise<DirectoryItem> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'getItem',
          data: { id }
        }
      })

      if (result.result && result.result.success) {
        return result.result.data
      } else {
        const errorMsg = result.result?.error || 'Failed to get item'
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('Error getting item:', error)
      throw error
    }
  }

  /**
   * 创建新项目
   */
  async createItem(data: {
    name: string
    type: 'folder' | 'note' | 'html'
    parentId?: string
    content?: string
  }): Promise<DirectoryItem> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'createItem',
          data: {
            ...data,
            parentId: data.parentId || 'root'
          }
        }
      })

      if (result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result.error || 'Failed to create item')
      }
    } catch (error) {
      console.error('Error creating item:', error)
      throw error
    }
  }
  
  /**
   * 更新项目
   */
  async updateItem(id: string, updates: Partial<DirectoryItem>): Promise<DirectoryItem> {
    await ensureAuth()
    
    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'updateItem',
          data: { id, updates }
        }
      })
      
      if (result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result.error || 'Failed to update item')
      }
    } catch (error) {
      console.error('Error updating item:', error)
      throw error
    }
  }
  
  /**
   * 删除项目
   */
  async deleteItem(id: string): Promise<void> {
    await ensureAuth()
    
    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'deleteItem',
          data: { id }
        }
      })
      
      if (!result.result.success) {
        throw new Error(result.result.error || 'Failed to delete item')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
      throw error
    }
  }
  
  /**
   * 移动项目
   */
  async moveItem(id: string, newParentId: string): Promise<void> {
    await ensureAuth()
    
    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'moveItem',
          data: { id, newParentId }
        }
      })
      
      if (!result.result.success) {
        throw new Error(result.result.error || 'Failed to move item')
      }
    } catch (error) {
      console.error('Error moving item:', error)
      throw error
    }
  }
  
  /**
   * 获取最近文件
   */
  async getRecentFiles(): Promise<DirectoryItem[]> {
    await ensureAuth()
    
    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'getRecentFiles'
        }
      })
      
      if (result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result.error || 'Failed to get recent files')
      }
    } catch (error) {
      console.error('Error getting recent files:', error)
      throw error
    }
  }
  
  /**
   * 获取分享文件
   */
  async getSharedFiles(): Promise<DirectoryItem[]> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'getSharedFiles'
        }
      })

      if (result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result.error || 'Failed to get shared files')
      }
    } catch (error) {
      console.error('Error getting shared files:', error)
      throw error
    }
  }

  /**
   * 获取回收站项目
   */
  async getTrashItems(): Promise<DirectoryItem[]> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'getTrashItems'
        }
      })

      if (result.result.success) {
        return result.result.data
      } else {
        throw new Error(result.result.error || 'Failed to get trash items')
      }
    } catch (error) {
      console.error('Error getting trash items:', error)
      throw error
    }
  }

  /**
   * 恢复项目从回收站
   */
  async restoreItem(id: string): Promise<void> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'restoreItem',
          data: { id }
        }
      })

      if (!result.result.success) {
        throw new Error(result.result.error || 'Failed to restore item')
      }
    } catch (error) {
      console.error('Error restoring item:', error)
      throw error
    }
  }

  /**
   * 永久删除项目
   */
  async permanentDelete(id: string): Promise<void> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'permanentDelete',
          data: { id }
        }
      })

      if (!result.result.success) {
        throw new Error(result.result.error || 'Failed to permanently delete item')
      }
    } catch (error) {
      console.error('Error permanently deleting item:', error)
      throw error
    }
  }

  /**
   * 清空回收站
   */
  async emptyTrash(): Promise<void> {
    await ensureAuth()

    try {
      const result = await app.callFunction({
        name: 'directoryTree',
        data: {
          action: 'emptyTrash'
        }
      })

      if (!result.result.success) {
        throw new Error(result.result.error || 'Failed to empty trash')
      }
    } catch (error) {
      console.error('Error emptying trash:', error)
      throw error
    }
  }
}

// 导出单例实例
export const directoryService = DirectoryService.getInstance()
