import React from 'react'
import { Routes, Route } from 'react-router-dom'

// 组件导入
import MainLayout from '../components/Layout/MainLayout'
import NotesView from '../components/Notes/NotesView'
import FoldersView from '../components/Folders/FoldersView'
import SettingsView from '../components/Settings/SettingsView'

const Dashboard: React.FC = () => {
  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<NotesView />} />
        <Route path="/notes" element={<NotesView />} />
        <Route path="/folders" element={<FoldersView />} />
        <Route path="/settings" element={<SettingsView />} />
      </Routes>
    </MainLayout>
  )
}

export default Dashboard
