{"name": "cloud-notes", "private": true, "version": "2.0.0", "type": "module", "description": "太阳系AI云笔记 - 基于CloudBase的智能笔记管理系统", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "serve": "npx live-server dist", "deploy": "npm run build && node scripts/deploy-mcp.js", "deploy:clean": "node scripts/deploy-mcp.js", "deploy:full": "npm run lint && npm run build && node scripts/deploy-mcp.js", "clean:hosting": "node -e \"console.log('请使用 npm run deploy 进行自动清理部署')\""}, "dependencies": {"@cloudbase/js-sdk": "^2.18.3", "@monaco-editor/react": "^4.6.0", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "date-fns": "^2.30.0", "dompurify": "^3.0.7", "framer-motion": "^10.16.16", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "lucide-react": "^0.294.0", "marked": "^11.1.1", "monaco-editor": "^0.45.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.20.1", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^9.1.3", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "three": "^0.159.0", "zustand": "^4.4.7"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.45.1", "@types/dompurify": "^3.0.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.159.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "keywords": ["cloudbase", "react", "typescript", "notes", "solar-system", "ai"], "author": "CloudBase AI ToolKit", "license": "MIT"}