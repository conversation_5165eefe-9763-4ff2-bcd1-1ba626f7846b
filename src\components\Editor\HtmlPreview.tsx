import React, { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { AlertTriangleIcon } from '../Icons/IconLibrary'

interface HtmlPreviewProps {
  content: string
  className?: string
}

const HtmlPreview: React.FC<HtmlPreviewProps> = ({ content, className = '' }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!iframeRef.current) return

    setIsLoading(true)
    setError(null)

    try {
      const iframe = iframeRef.current
      const doc = iframe.contentDocument || iframe.contentWindow?.document

      if (!doc) {
        setError('无法访问iframe文档')
        setIsLoading(false)
        return
      }

      // 清空文档
      doc.open()
      
      // 直接写入HTML内容，支持完整的HTML、CSS和JavaScript
      doc.write(content)
      doc.close()

      setIsLoading(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : '渲染HTML时发生错误')
      setIsLoading(false)
    }
  }, [content])



  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center h-full p-8 ${className}`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <AlertTriangleIcon className="w-12 h-12 text-theme-error mx-auto mb-4" />
          <h3 className="text-lg font-medium text-theme-text mb-2">HTML预览错误</h3>
          <p className="text-theme-text-muted mb-4">{error}</p>
          <div className="text-xs text-theme-text-muted bg-theme-surface p-3 rounded-lg">
            请检查HTML语法是否正确
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className={`relative h-full overflow-hidden ${className}`} style={{ background: 'transparent' }}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-theme-bg/80 z-10">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center gap-2 text-theme-text-muted"
          >
            <div className="w-4 h-4 border-2 border-theme-primary border-t-transparent rounded-full animate-spin" />
            <span className="text-sm">正在渲染HTML...</span>
          </motion.div>
        </div>
      )}
      
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        title="HTML预览"
        sandbox="allow-same-origin allow-scripts"
        style={{
          background: 'transparent'
        }}
      />
      

    </div>
  )
}

export default HtmlPreview
