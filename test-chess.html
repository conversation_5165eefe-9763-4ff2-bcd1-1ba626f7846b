<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太阳系象棋</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
            color: #fff8dc;
            overflow: hidden;
            position: relative;
        }

        /* 星空背景 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #fdb813, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.6), transparent),
                radial-gradient(1px 1px at 130px 80px, #4169e1, transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.7), transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: twinkle 4s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes twinkle {
            0% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .game-board {
            background: rgba(26, 26, 46, 0.9);
            border: 3px solid #fdb813;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 
                0 0 50px rgba(253, 184, 19, 0.3),
                inset 0 0 30px rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(10px);
        }

        .title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: bold;
            color: #fdb813;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(253, 184, 19, 0.8);
        }

        .subtitle {
            text-align: center;
            font-size: 1rem;
            color: #e0ffff;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .chess-board {
            display: grid;
            grid-template-columns: repeat(9, 60px);
            grid-template-rows: repeat(10, 60px);
            gap: 2px;
            background: #2a2a3e;
            padding: 10px;
            border-radius: 10px;
            position: relative;
        }

        .chess-cell {
            width: 60px;
            height: 60px;
            background: rgba(42, 42, 62, 0.8);
            border: 1px solid #fdb813;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .chess-cell:hover {
            background: rgba(253, 184, 19, 0.2);
            box-shadow: 0 0 15px rgba(253, 184, 19, 0.5);
        }

        .chess-piece {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .red-piece {
            background: linear-gradient(135deg, #cd5c5c, #ff6b6b);
            color: #fff;
            border: 2px solid #ff4757;
        }

        .black-piece {
            background: linear-gradient(135deg, #4169e1, #5a7fff);
            color: #fff;
            border: 2px solid #3742fa;
        }

        .chess-piece:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .game-info {
            margin-left: 40px;
            background: rgba(26, 26, 46, 0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #fdb813;
            min-width: 250px;
        }

        .info-title {
            color: #fdb813;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-label {
            color: #e0ffff;
            font-size: 0.9rem;
        }

        .info-value {
            color: #fdb813;
            font-weight: bold;
        }

        .game-container {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        /* 河界线 */
        .river-line {
            position: absolute;
            left: 10px;
            right: 10px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fdb813, transparent);
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="game-board">
            <h1 class="title">太阳系象棋</h1>
            <p class="subtitle">行星战争 · 宇宙征战</p>
            
            <div class="game-container">
                <div class="chess-board" id="chessBoard">
                    <div class="river-line"></div>
                    <!-- 棋盘格子将通过JavaScript生成 -->
                </div>
                
                <div class="game-info">
                    <div class="info-title">
                        🚀 航行日志
                    </div>
                    <div class="info-item">
                        <span class="info-label">任务状态:</span>
                        <span class="info-value">太阳系未来</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">轨道周期:</span>
                        <span class="info-value">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">宇宙时间:</span>
                        <span class="info-value">01:07</span>
                    </div>
                    <div style="margin-top: 20px; font-size: 0.8rem; color: #a0a0a0; text-align: center;">
                        星空静谧，等待探索...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化棋盘
        function initBoard() {
            const board = document.getElementById('chessBoard');
            
            // 创建10x9的棋盘格子
            for (let row = 0; row < 10; row++) {
                for (let col = 0; col < 9; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'chess-cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    board.appendChild(cell);
                }
            }
            
            // 放置初始棋子
            placePieces();
        }
        
        // 放置棋子
        function placePieces() {
            const pieces = [
                // 红方（下方）
                {row: 9, col: 0, type: '车', color: 'red'},
                {row: 9, col: 1, type: '马', color: 'red'},
                {row: 9, col: 2, type: '相', color: 'red'},
                {row: 9, col: 3, type: '仕', color: 'red'},
                {row: 9, col: 4, type: '帅', color: 'red'},
                {row: 9, col: 5, type: '仕', color: 'red'},
                {row: 9, col: 6, type: '相', color: 'red'},
                {row: 9, col: 7, type: '马', color: 'red'},
                {row: 9, col: 8, type: '车', color: 'red'},
                {row: 7, col: 1, type: '炮', color: 'red'},
                {row: 7, col: 7, type: '炮', color: 'red'},
                {row: 6, col: 0, type: '兵', color: 'red'},
                {row: 6, col: 2, type: '兵', color: 'red'},
                {row: 6, col: 4, type: '兵', color: 'red'},
                {row: 6, col: 6, type: '兵', color: 'red'},
                {row: 6, col: 8, type: '兵', color: 'red'},
                
                // 黑方（上方）
                {row: 0, col: 0, type: '车', color: 'black'},
                {row: 0, col: 1, type: '马', color: 'black'},
                {row: 0, col: 2, type: '象', color: 'black'},
                {row: 0, col: 3, type: '士', color: 'black'},
                {row: 0, col: 4, type: '将', color: 'black'},
                {row: 0, col: 5, type: '士', color: 'black'},
                {row: 0, col: 6, type: '象', color: 'black'},
                {row: 0, col: 7, type: '马', color: 'black'},
                {row: 0, col: 8, type: '车', color: 'black'},
                {row: 2, col: 1, type: '炮', color: 'black'},
                {row: 2, col: 7, type: '炮', color: 'black'},
                {row: 3, col: 0, type: '卒', color: 'black'},
                {row: 3, col: 2, type: '卒', color: 'black'},
                {row: 3, col: 4, type: '卒', color: 'black'},
                {row: 3, col: 6, type: '卒', color: 'black'},
                {row: 3, col: 8, type: '卒', color: 'black'},
            ];
            
            pieces.forEach(piece => {
                const cell = document.querySelector(`[data-row="${piece.row}"][data-col="${piece.col}"]`);
                if (cell) {
                    const pieceElement = document.createElement('div');
                    pieceElement.className = `chess-piece ${piece.color}-piece`;
                    pieceElement.textContent = piece.type;
                    cell.appendChild(pieceElement);
                }
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initBoard);
    </script>
</body>
</html>
