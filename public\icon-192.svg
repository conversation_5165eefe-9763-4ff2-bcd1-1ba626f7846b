<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 高分辨率云笔记图标 - 深色主题优化 -->
  
  <!-- 背景圆形 -->
  <rect width="192" height="192" rx="24" fill="#0d1117"/>
  
  <!-- 主云朵形状 -->
  <path d="M51 126c-15 0-27-12-27-27 0-12 7.8-22.2 19.2-25.2C45 61.2 54 51 66 51c7.8 0 14.4 4.2 18 10.8 4.8-2.4 10.2-3.6 16.2-3.6 19.2 0 34.8 15.6 34.8 34.8 0 2.4-0.6 4.8-1.2 6.6 11.4 2.4 20.4 12.6 20.4 24.6 0 13.8-11.4 25.2-25.2 25.2H51z" fill="#58a6ff" stroke="#7dd3fc" stroke-width="3"/>
  
  <!-- 文档主体 -->
  <rect x="66" y="78" width="60" height="72" rx="9" fill="#f0f6fc" stroke="#58a6ff" stroke-width="6"/>
  
  <!-- 文档折角 -->
  <path d="M114 78 L114 96 L132 96 Z" fill="#e2e8f0" stroke="#58a6ff" stroke-width="3"/>
  
  <!-- 文档内容线条 -->
  <line x1="78" y1="96" x2="102" y2="96" stroke="#58a6ff" stroke-width="6" opacity="0.8"/>
  <line x1="78" y1="108" x2="114" y2="108" stroke="#58a6ff" stroke-width="6" opacity="0.8"/>
  <line x1="78" y1="120" x2="96" y2="120" stroke="#58a6ff" stroke-width="6" opacity="0.8"/>
  <line x1="78" y1="132" x2="108" y2="132" stroke="#58a6ff" stroke-width="6" opacity="0.8"/>
  
  <!-- 云端数据点 -->
  <circle cx="150" cy="54" r="9" fill="#7dd3fc" opacity="0.9"/>
  <circle cx="132" cy="42" r="6" fill="#7dd3fc" opacity="0.7"/>
  <circle cx="162" cy="72" r="4.8" fill="#7dd3fc" opacity="0.8"/>
  
  <!-- 数据流连接线 -->
  <path d="M141 51 Q126 60 120 72" stroke="#7dd3fc" stroke-width="6" opacity="0.6" fill="none"/>
  <path d="M153 63 Q156 66 157.2 67.2" stroke="#7dd3fc" stroke-width="6" opacity="0.6" fill="none"/>
  
  <!-- 云端同步指示器 -->
  <circle cx="36" cy="48" r="6" fill="#22d3ee" opacity="0.8"/>
  <circle cx="48" cy="36" r="4.2" fill="#22d3ee" opacity="0.6"/>
  <path d="M40.8 43.2 Q43.2 40.8 43.8 40.2" stroke="#22d3ee" stroke-width="4.8" opacity="0.7" fill="none"/>
  
  <!-- 装饰性元素 -->
  <circle cx="30" cy="150" r="3" fill="#58a6ff" opacity="0.4"/>
  <circle cx="162" cy="150" r="2.4" fill="#58a6ff" opacity="0.3"/>
  <circle cx="150" cy="30" r="2.1" fill="#7dd3fc" opacity="0.5"/>
</svg>
