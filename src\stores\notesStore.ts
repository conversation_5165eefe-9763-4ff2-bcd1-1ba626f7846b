import { create } from 'zustand'

export interface Note {
  id: string
  title: string
  content: string
  type: 'markdown' | 'html'
  folderId?: string
  tags: string[]
  category: 'mercury' | 'venus' | 'earth' | 'mars' | 'jupiter' | 'saturn'
  importance: number // 1-10
  isShared: boolean
  shareConfig?: {
    shareId: string
    password?: string
    expiresAt?: string
    allowEdit: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Folder {
  id: string
  name: string
  parentId?: string
  children: string[]
  noteIds: string[]
  color?: string
  icon?: string
  createdAt: string
  updatedAt: string
}

export interface OpenTab {
  id: string
  noteId: string
  title: string
  hasUnsavedChanges: boolean
  content: string
  lastSavedContent: string
}

interface NotesState {
  notes: Note[]
  folders: Folder[]
  activeNoteId: string | null
  activeFolderId: string | null
  searchQuery: string
  selectedTags: string[]
  currentPlanet: string

  // 标签页管理
  openTabs: OpenTab[]
  activeTabId: string | null

  // Actions
  setNotes: (notes: Note[]) => void
  setFolders: (folders: Folder[]) => void
  addNote: (note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateNote: (id: string, updates: Partial<Note>) => void
  deleteNote: (id: string) => void
  setActiveNote: (id: string | null) => void

  addFolder: (folder: Omit<Folder, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateFolder: (id: string, updates: Partial<Folder>) => void
  deleteFolder: (id: string) => void
  setActiveFolder: (id: string | null) => void

  setSearchQuery: (query: string) => void
  setSelectedTags: (tags: string[]) => void
  setCurrentPlanet: (planet: string) => void

  // 标签页操作
  openNoteInTab: (noteId: string) => void
  openDirectoryItemInTab: (item: any) => void // 新增：从DirectoryItem打开文件
  closeTab: (tabId: string) => void
  setActiveTab: (tabId: string) => void
  updateTabContent: (tabId: string, content: string) => void
  markTabSaved: (tabId: string) => void
  saveTabToCloud: (tabId: string) => Promise<void>
  getActiveTab: () => OpenTab | null

  // Computed
  getFilteredNotes: () => Note[]
  getFolderTree: () => Folder[]
  getNotesInFolder: (folderId: string) => Note[]
}

// 初始化测试数据
const initialNotes: Note[] = [
  {
    id: 'note_1',
    title: '项目计划.md',
    content: '# 项目计划\n\n## 目标\n\n这是一个示例笔记，用于测试编辑器功能。\n\n## 任务列表\n\n- [x] 完成编辑器界面设计\n- [x] 实现GitHub风格工具栏\n- [x] 添加标签页切换功能\n- [ ] 集成云存储\n- [ ] 添加协作功能\n\n## 代码示例\n\n```javascript\nconst editor = new MonacoEditor({\n  language: \'markdown\',\n  theme: \'vs-dark\'\n});\n```\n\n> 这是一个引用块，用于强调重要信息。\n\n**粗体文本** 和 *斜体文本* 的示例。',
    type: 'markdown',
    tags: ['项目', '计划'],
    category: 'earth',
    importance: 8,
    isShared: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin'
  },
  {
    id: 'note_2',
    title: '学习笔记.md',
    content: '# 学习笔记\n\n## React Hooks\n\n### useState\n\n```jsx\nconst [count, setCount] = useState(0);\n```\n\n### useEffect\n\n```jsx\nuseEffect(() => {\n  // 副作用逻辑\n}, [dependencies]);\n```\n\n## TypeScript\n\n### 接口定义\n\n```typescript\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n}\n```\n\n### 泛型\n\n```typescript\nfunction identity<T>(arg: T): T {\n  return arg;\n}\n```',
    type: 'markdown',
    tags: ['学习', 'React', 'TypeScript'],
    category: 'earth',
    importance: 7,
    isShared: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin'
  },
  {
    id: 'note_3',
    title: '会议记录.md',
    content: '# 会议记录\n\n**日期**: 2025年7月19日\n**参与者**: 开发团队\n**主题**: 云笔记功能讨论\n\n## 讨论要点\n\n1. **编辑器增强**\n   - GitHub风格界面 ✅\n   - 工具栏功能 ✅\n   - 手动保存机制 ✅\n\n2. **用户体验优化**\n   - 深色主题适配 ✅\n   - 响应式设计 ✅\n   - 图标系统重设计 ✅\n\n3. **下一步计划**\n   - 云存储集成\n   - 多人协作\n   - 移动端适配\n\n## 行动项\n\n| 任务 | 负责人 | 截止日期 |\n|------|--------|----------|\n| 云存储API | 后端团队 | 下周五 |\n| 协作功能 | 前端团队 | 下下周 |\n| 测试用例 | QA团队 | 本周末 |\n\n## 备注\n\n> 所有功能都需要考虑性能和用户体验。',
    type: 'markdown',
    tags: ['会议', '记录'],
    category: 'earth',
    importance: 6,
    isShared: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin'
  },
  {
    id: 'note_4',
    title: '太阳系象棋.html',
    content: `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太阳系象棋</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
            color: #fff8dc;
            overflow: hidden;
            position: relative;
        }

        /* 星空背景 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fdb813, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.6), transparent),
                radial-gradient(1px 1px at 130px 80px, #4169e1, transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.7), transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: twinkle 4s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes twinkle {
            0% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .game-board {
            background: rgba(26, 26, 46, 0.9);
            border: 3px solid #fdb813;
            border-radius: 20px;
            padding: 30px;
            box-shadow:
                0 0 50px rgba(253, 184, 19, 0.3),
                inset 0 0 30px rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(10px);
        }

        .title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: bold;
            color: #fdb813;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(253, 184, 19, 0.8);
        }

        .subtitle {
            text-align: center;
            font-size: 1rem;
            color: #e0ffff;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .chess-board {
            display: grid;
            grid-template-columns: repeat(9, 60px);
            grid-template-rows: repeat(10, 60px);
            gap: 2px;
            background: #2a2a3e;
            padding: 10px;
            border-radius: 10px;
            position: relative;
        }

        .chess-cell {
            width: 60px;
            height: 60px;
            background: rgba(42, 42, 62, 0.8);
            border: 1px solid #fdb813;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .chess-cell:hover {
            background: rgba(253, 184, 19, 0.2);
            box-shadow: 0 0 15px rgba(253, 184, 19, 0.5);
        }

        .chess-piece {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .red-piece {
            background: linear-gradient(135deg, #cd5c5c, #ff6b6b);
            color: #fff;
            border: 2px solid #ff4757;
        }

        .black-piece {
            background: linear-gradient(135deg, #4169e1, #5a7fff);
            color: #fff;
            border: 2px solid #3742fa;
        }

        .chess-piece:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .game-info {
            margin-left: 40px;
            background: rgba(26, 26, 46, 0.8);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #fdb813;
            min-width: 250px;
        }

        .info-title {
            color: #fdb813;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-label {
            color: #e0ffff;
            font-size: 0.9rem;
        }

        .info-value {
            color: #fdb813;
            font-weight: bold;
        }

        .game-container {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        /* 河界线 */
        .river-line {
            position: absolute;
            left: 10px;
            right: 10px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #fdb813, transparent);
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="game-board">
            <h1 class="title">太阳系象棋</h1>
            <p class="subtitle">行星战争 · 宇宙征战</p>

            <div class="game-container">
                <div class="chess-board" id="chessBoard">
                    <div class="river-line"></div>
                </div>

                <div class="game-info">
                    <div class="info-title">
                        🚀 航行日志
                    </div>
                    <div class="info-item">
                        <span class="info-label">任务状态:</span>
                        <span class="info-value">太阳系未来</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">轨道周期:</span>
                        <span class="info-value">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">宇宙时间:</span>
                        <span class="info-value">01:07</span>
                    </div>
                    <div style="margin-top: 20px; font-size: 0.8rem; color: #a0a0a0; text-align: center;">
                        星空静谧，等待探索...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function initBoard() {
            const board = document.getElementById('chessBoard');

            for (let row = 0; row < 10; row++) {
                for (let col = 0; col < 9; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'chess-cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    board.appendChild(cell);
                }
            }

            placePieces();
        }

        function placePieces() {
            const pieces = [
                {row: 9, col: 0, type: '车', color: 'red'},
                {row: 9, col: 1, type: '马', color: 'red'},
                {row: 9, col: 2, type: '相', color: 'red'},
                {row: 9, col: 3, type: '仕', color: 'red'},
                {row: 9, col: 4, type: '帅', color: 'red'},
                {row: 9, col: 5, type: '仕', color: 'red'},
                {row: 9, col: 6, type: '相', color: 'red'},
                {row: 9, col: 7, type: '马', color: 'red'},
                {row: 9, col: 8, type: '车', color: 'red'},
                {row: 7, col: 1, type: '炮', color: 'red'},
                {row: 7, col: 7, type: '炮', color: 'red'},
                {row: 6, col: 0, type: '兵', color: 'red'},
                {row: 6, col: 2, type: '兵', color: 'red'},
                {row: 6, col: 4, type: '兵', color: 'red'},
                {row: 6, col: 6, type: '兵', color: 'red'},
                {row: 6, col: 8, type: '兵', color: 'red'},

                {row: 0, col: 0, type: '车', color: 'black'},
                {row: 0, col: 1, type: '马', color: 'black'},
                {row: 0, col: 2, type: '象', color: 'black'},
                {row: 0, col: 3, type: '士', color: 'black'},
                {row: 0, col: 4, type: '将', color: 'black'},
                {row: 0, col: 5, type: '士', color: 'black'},
                {row: 0, col: 6, type: '象', color: 'black'},
                {row: 0, col: 7, type: '马', color: 'black'},
                {row: 0, col: 8, type: '车', color: 'black'},
                {row: 2, col: 1, type: '炮', color: 'black'},
                {row: 2, col: 7, type: '炮', color: 'black'},
                {row: 3, col: 0, type: '卒', color: 'black'},
                {row: 3, col: 2, type: '卒', color: 'black'},
                {row: 3, col: 4, type: '卒', color: 'black'},
                {row: 3, col: 6, type: '卒', color: 'black'},
                {row: 3, col: 8, type: '卒', color: 'black'},
            ];

            pieces.forEach(piece => {
                const cell = document.querySelector(\`[data-row="\${piece.row}"][data-col="\${piece.col}"]\`);
                if (cell) {
                    const pieceElement = document.createElement('div');
                    pieceElement.className = \`chess-piece \${piece.color}-piece\`;
                    pieceElement.textContent = piece.type;
                    cell.appendChild(pieceElement);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', initBoard);
    </script>
</body>
</html>`,
    type: 'html',
    tags: ['游戏', '象棋', '太阳系'],
    category: 'jupiter',
    importance: 9,
    isShared: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin'
  },
  {
    id: 'note_5',
    title: '新建HTML文档.html',
    content: '',
    type: 'html',
    tags: ['HTML', '新建'],
    category: 'earth',
    importance: 5,
    isShared: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'admin'
  }
]

export const useNotesStore = create<NotesState>((set, get) => ({
  notes: initialNotes,
  folders: [],
  activeNoteId: 'note_5', // 自动打开新建的HTML文档
  activeFolderId: null,
  searchQuery: '',
  selectedTags: [],
  currentPlanet: 'earth',

  // 标签页状态
  openTabs: [
    {
      id: 'tab_note_5',
      noteId: 'note_5',
      title: '新建HTML文档.html',
      hasUnsavedChanges: false,
      content: '',
      lastSavedContent: ''
    }
  ],
  activeTabId: 'tab_note_5',

  setNotes: (notes) => set({ notes }),
  setFolders: (folders) => set({ folders }),

  addNote: (noteData) => {
    const newNote: Note = {
      ...noteData,
      id: `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    set((state) => ({
      notes: [...state.notes, newNote],
      activeNoteId: newNote.id
    }))
  },

  updateNote: (id, updates) => {
    set((state) => ({
      notes: state.notes.map(note =>
        note.id === id
          ? { ...note, ...updates, updatedAt: new Date().toISOString() }
          : note
      )
    }))
  },

  deleteNote: (id) => {
    set((state) => ({
      notes: state.notes.filter(note => note.id !== id),
      activeNoteId: state.activeNoteId === id ? null : state.activeNoteId
    }))
  },

  setActiveNote: (id) => set({ activeNoteId: id }),

  addFolder: (folderData) => {
    const newFolder: Folder = {
      ...folderData,
      id: `folder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      children: [],
      noteIds: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    set((state) => ({
      folders: [...state.folders, newFolder],
      activeFolderId: newFolder.id
    }))
  },

  updateFolder: (id, updates) => {
    set((state) => ({
      folders: state.folders.map(folder =>
        folder.id === id
          ? { ...folder, ...updates, updatedAt: new Date().toISOString() }
          : folder
      )
    }))
  },

  deleteFolder: (id) => {
    set((state) => ({
      folders: state.folders.filter(folder => folder.id !== id),
      activeFolderId: state.activeFolderId === id ? null : state.activeFolderId
    }))
  },

  setActiveFolder: (id) => set({ activeFolderId: id }),

  setSearchQuery: (query) => set({ searchQuery: query }),
  setSelectedTags: (tags) => set({ selectedTags: tags }),
  setCurrentPlanet: (planet) => set({ currentPlanet: planet }),

  // 标签页操作
  openNoteInTab: (noteId) => {
    const { notes, openTabs, activeTabId } = get()
    const note = notes.find(n => n.id === noteId)
    if (!note) {
      console.warn('Note not found:', noteId)
      return
    }

    // 检查是否已经打开
    const existingTab = openTabs.find(tab => tab.noteId === noteId)
    if (existingTab) {
      // 如果已经是活跃标签页，不需要重复设置
      if (activeTabId === existingTab.id) {
        return
      }
      set({ activeTabId: existingTab.id, activeNoteId: noteId })
      return
    }

    // 创建新标签页
    const newTab: OpenTab = {
      id: `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      noteId: note.id,
      title: note.title,
      hasUnsavedChanges: false,
      content: note.content,
      lastSavedContent: note.content
    }

    set((state) => ({
      openTabs: [...state.openTabs, newTab],
      activeTabId: newTab.id,
      activeNoteId: noteId
    }))
  },

  // 从DirectoryItem打开文件
  openDirectoryItemInTab: async (item) => {
    const { openTabs, activeTabId } = get()

    // 只处理文件，不处理文件夹
    if (item.type === 'folder') {
      return
    }

    // 检查是否已经打开
    const existingTab = openTabs.find(tab => tab.noteId === item._id)
    if (existingTab) {
      // 如果已经是活跃标签页，不需要重复设置
      if (activeTabId === existingTab.id) {
        return
      }
      set({ activeTabId: existingTab.id, activeNoteId: item._id })
      return
    }

    // 从云数据库加载文件内容
    let content = item.content || ''
    try {
      // 如果文件内容为空，尝试从云数据库加载
      if (!content) {
        const { directoryService } = await import('../services/directoryService')
        const fullItem = await directoryService.getItem(item._id)
        content = fullItem.content || ''
      }
    } catch (error) {
      console.error('Failed to load file content:', error)
      content = '' // 使用空内容作为fallback
    }

    // 创建新标签页
    const newTab: OpenTab = {
      id: `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      noteId: item._id,
      title: item.name,
      hasUnsavedChanges: false,
      content: content,
      lastSavedContent: content
    }

    set((state) => ({
      openTabs: [...state.openTabs, newTab],
      activeTabId: newTab.id,
      activeNoteId: item._id
    }))
  },

  closeTab: (tabId) => {
    const { openTabs, activeTabId } = get()
    const tabIndex = openTabs.findIndex(tab => tab.id === tabId)
    if (tabIndex === -1) return

    const newTabs = openTabs.filter(tab => tab.id !== tabId)
    let newActiveTabId = activeTabId

    // 如果关闭的是当前活跃标签页，需要选择新的活跃标签页
    if (activeTabId === tabId) {
      if (newTabs.length > 0) {
        // 选择相邻的标签页
        const newActiveIndex = Math.min(tabIndex, newTabs.length - 1)
        newActiveTabId = newTabs[newActiveIndex]?.id || null
      } else {
        newActiveTabId = null
      }
    }

    set({
      openTabs: newTabs,
      activeTabId: newActiveTabId,
      activeNoteId: newActiveTabId ? newTabs.find(tab => tab.id === newActiveTabId)?.noteId || null : null
    })
  },

  setActiveTab: (tabId) => {
    const { openTabs } = get()
    const tab = openTabs.find(t => t.id === tabId)
    if (tab) {
      set({ activeTabId: tabId, activeNoteId: tab.noteId })
    }
  },

  updateTabContent: (tabId, content) => {
    set((state) => ({
      openTabs: state.openTabs.map(tab =>
        tab.id === tabId
          ? {
              ...tab,
              content,
              hasUnsavedChanges: content !== tab.lastSavedContent
            }
          : tab
      )
    }))
  },

  markTabSaved: (tabId) => {
    set((state) => ({
      openTabs: state.openTabs.map(tab =>
        tab.id === tabId
          ? {
              ...tab,
              hasUnsavedChanges: false,
              lastSavedContent: tab.content
            }
          : tab
      )
    }))
  },

  // 保存标签页内容到云数据库
  saveTabToCloud: async (tabId) => {
    const { openTabs } = get()
    const tab = openTabs.find(t => t.id === tabId)
    if (!tab) {
      throw new Error('Tab not found')
    }

    try {
      const { directoryService } = await import('../services/directoryService')
      await directoryService.updateItem(tab.noteId, {
        content: tab.content
      })

      // 标记为已保存
      get().markTabSaved(tabId)
    } catch (error) {
      console.error('Failed to save tab to cloud:', error)
      throw error
    }
  },

  getActiveTab: () => {
    const { openTabs, activeTabId } = get()
    return openTabs.find(tab => tab.id === activeTabId) || null
  },

  getFilteredNotes: () => {
    const { notes, searchQuery, selectedTags, currentPlanet } = get()
    
    return notes.filter(note => {
      // 搜索过滤
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        if (!note.title.toLowerCase().includes(query) && 
            !note.content.toLowerCase().includes(query)) {
          return false
        }
      }
      
      // 标签过滤
      if (selectedTags.length > 0) {
        if (!selectedTags.some(tag => note.tags.includes(tag))) {
          return false
        }
      }
      
      // 行星分类过滤
      if (currentPlanet !== 'all' && note.category !== currentPlanet) {
        return false
      }
      
      return true
    })
  },

  getFolderTree: () => {
    const { folders } = get()
    
    // 构建文件夹树结构
    const rootFolders = folders.filter(folder => !folder.parentId)
    
    const buildTree = (parentFolders: Folder[]): Folder[] => {
      return parentFolders.map(folder => ({
        ...folder,
        children: folders.filter(f => f.parentId === folder.id).map(f => f.id)
      }))
    }
    
    return buildTree(rootFolders)
  },

  getNotesInFolder: (folderId) => {
    const { notes } = get()
    return notes.filter(note => note.folderId === folderId)
  }
}))
