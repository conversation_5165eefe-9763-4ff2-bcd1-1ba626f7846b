import React from 'react'
import { Setting<PERSON>, User, <PERSON><PERSON>, <PERSON> } from 'lucide-react'

const SettingsView: React.FC = () => {
  return (
    <div className="h-full bg-cosmic-void p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-sun-corona mb-2">太空站控制室</h1>
          <p className="text-venus-cloud/80">配置您的太阳系知识站</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 设置导航 */}
          <div className="lg:col-span-1">
            <div className="bg-starfield/60 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-4">
              <h2 className="text-lg font-semibold text-venus-cloud mb-4 flex items-center gap-2">
                <Settings className="w-5 h-5" />
                系统控制
              </h2>
              
              <nav className="space-y-2">
                <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg bg-sun-corona/20 text-sun-corona">
                  <User className="w-4 h-4" />
                  <span className="text-sm">宇航员档案</span>
                </button>
                <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-venus-cloud hover:bg-sun-corona/10 hover:text-sun-corona transition-colors">
                  <Palette className="w-4 h-4" />
                  <span className="text-sm">视觉主题</span>
                </button>
                <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-venus-cloud hover:bg-sun-corona/10 hover:text-sun-corona transition-colors">
                  <Globe className="w-4 h-4" />
                  <span className="text-sm">轨道设置</span>
                </button>
              </nav>
            </div>
          </div>

          {/* 设置内容 */}
          <div className="lg:col-span-2">
            <div className="bg-starfield/60 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-venus-cloud mb-6">宇航员档案</h3>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-venus-cloud mb-2">用户名</label>
                  <input 
                    type="text" 
                    value="admin"
                    className="w-full px-3 py-2 bg-cosmic-void/50 border border-sun-corona/20 rounded-lg text-venus-cloud focus:border-sun-corona focus:outline-none"
                    readOnly
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-venus-cloud mb-2">邮箱</label>
                  <input 
                    type="email" 
                    value="<EMAIL>"
                    className="w-full px-3 py-2 bg-cosmic-void/50 border border-sun-corona/20 rounded-lg text-venus-cloud focus:border-sun-corona focus:outline-none"
                    readOnly
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-venus-cloud mb-2">角色</label>
                  <div className="px-3 py-2 bg-earth-blue/20 border border-earth-blue/30 rounded-lg text-earth-blue">
                    系统管理员
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-venus-cloud mb-2">注册时间</label>
                  <div className="px-3 py-2 bg-cosmic-void/30 border border-sun-corona/20 rounded-lg text-venus-cloud/80">
                    {new Date().toLocaleDateString('zh-CN')}
                  </div>
                </div>
              </div>
              
              <div className="mt-8 pt-6 border-t border-sun-corona/20">
                <button className="px-4 py-2 bg-sun-corona/20 hover:bg-sun-corona/30 text-sun-corona rounded-lg transition-colors">
                  保存设置
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 功能开发中提示 */}
        <div className="mt-8 bg-jupiter-storm/20 border border-jupiter-storm/40 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-jupiter-storm mb-2">
            🚧 功能开发中
          </h3>
          <p className="text-venus-cloud/80">
            设置页面的详细功能正在开发中，包括主题切换、语言设置、快捷键配置等功能。
          </p>
        </div>
      </div>
    </div>
  )
}

export default SettingsView
