import React from 'react'
import { FolderOpen, Plus, Folder } from 'lucide-react'

const FoldersView: React.FC = () => {
  return (
    <div className="h-full bg-cosmic-void p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-sun-corona mb-2">星系文件夹</h1>
            <p className="text-venus-cloud/80">组织您的知识星球</p>
          </div>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-sun-corona/20 hover:bg-sun-corona/30 text-sun-corona rounded-lg transition-colors">
            <Plus className="w-5 h-5" />
            <span>新建文件夹</span>
          </button>
        </div>

        {/* 空状态 */}
        <div className="text-center py-20">
          <div className="w-24 h-24 bg-sun-corona/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <FolderOpen className="w-12 h-12 text-sun-corona" />
          </div>
          <h2 className="text-2xl font-semibold text-venus-cloud mb-4">还没有文件夹</h2>
          <p className="text-venus-cloud/60 mb-8 max-w-md mx-auto">
            创建文件夹来组织您的笔记，就像在太阳系中建立不同的星球基地一样
          </p>
          <button className="flex items-center gap-2 px-6 py-3 bg-sun-corona/20 hover:bg-sun-corona/30 text-sun-corona rounded-lg transition-colors mx-auto">
            <Folder className="w-5 h-5" />
            <span>创建第一个文件夹</span>
          </button>
        </div>

        {/* 功能开发中提示 */}
        <div className="mt-12 bg-jupiter-storm/20 border border-jupiter-storm/40 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-jupiter-storm mb-2">
            🚧 功能开发中
          </h3>
          <p className="text-venus-cloud/80">
            文件夹管理功能正在开发中，将支持无限层级嵌套、拖拽排序、批量操作等功能。
          </p>
        </div>
      </div>
    </div>
  )
}

export default FoldersView
