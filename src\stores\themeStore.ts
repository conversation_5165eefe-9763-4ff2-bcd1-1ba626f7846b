import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ThemeId = 'solar'

export interface ThemeColors {
  // 主要色彩
  primary: string
  secondary: string
  accent: string
  
  // 背景色彩
  background: string
  surface: string
  void: string
  
  // 文本色彩
  text: string
  textSecondary: string
  textMuted: string
  
  // 特殊效果色彩
  glow: string
  highlight: string
  shadow: string
}

export interface ThemeConfig {
  id: ThemeId
  name: string
  description: string
  icon: string
  colors: ThemeColors
  effects: string[]
  animations: {
    duration: number
    easing: string
  }
}

export interface ThemeState {
  currentTheme: ThemeId
  themes: Record<ThemeId, ThemeConfig>
  isTransitioning: boolean
  
  // Actions
  setTheme: (themeId: ThemeId) => void
  getTheme: (themeId?: ThemeId) => ThemeConfig
  applyTheme: (themeId: ThemeId) => void
}

// 主题配置定义
const themes: Record<ThemeId, ThemeConfig> = {
  solar: {
    id: 'solar',
    name: '太阳系',
    description: '温暖的太阳系主题，以太阳的金色光辉为核心',
    icon: 'sun',
    colors: {
      primary: '#FDB813',      // 太阳金
      secondary: '#4169E1',    // 地球蓝
      accent: '#CD5C5C',       // 火星红
      background: '#0F0F23',   // 宇宙虚空
      surface: '#1A1A2E',     // 星场背景
      void: '#0F0F23',        // 深空
      text: '#FFF8DC',        // 金星云白
      textSecondary: '#E0FFFF', // 彗星尾迹
      textMuted: '#A0A0A0',   // 小行星带灰
      glow: '#FDB813',        // 太阳光晕
      highlight: '#FFE4B5',   // 太阳风
      shadow: '#1A1A2E'       // 星场阴影
    },
    effects: ['solar-flares', 'planetary-orbits', 'starfield'],
    animations: {
      duration: 1000,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      currentTheme: 'solar',
      themes,
      isTransitioning: false,

      setTheme: (themeId: ThemeId) => {
        const { currentTheme, applyTheme } = get()
        
        if (currentTheme === themeId) return
        
        set({ isTransitioning: true })
        
        // 应用主题
        applyTheme(themeId)
        
        // 设置新主题
        set({ 
          currentTheme: themeId,
          isTransitioning: false 
        })
      },

      getTheme: (themeId?: ThemeId) => {
        const { currentTheme, themes } = get()
        return themes[themeId || currentTheme]
      },

      applyTheme: (themeId: ThemeId) => {
        const theme = themes[themeId]
        if (!theme) return

        // 更新CSS变量
        const root = document.documentElement
        
        // 应用主题色彩
        Object.entries(theme.colors).forEach(([key, value]) => {
          root.style.setProperty(`--theme-${key}`, value)
        })
        
        // 设置主题标识
        root.setAttribute('data-theme', themeId)
        
        // 触发主题切换事件
        window.dispatchEvent(new CustomEvent('themeChange', {
          detail: { theme: themeId, config: theme }
        }))
      }
    }),
    {
      name: 'solar-notes-theme',
      partialize: (state) => ({
        currentTheme: state.currentTheme
      })
    }
  )
)
