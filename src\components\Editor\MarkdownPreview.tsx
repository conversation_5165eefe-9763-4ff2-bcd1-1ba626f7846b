import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkBreaks from 'remark-breaks'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { useTheme } from '../../hooks/useTheme'

interface MarkdownPreviewProps {
  content: string
  className?: string
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({
  content,
  className = ''
}) => {
  const { theme } = useTheme()
  const containerRef = useRef<HTMLDivElement>(null)

  // 确保预览模式总是从顶部开始显示
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0
    }
  }, [content])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
      ref={containerRef}
      className={`
        h-full overflow-y-auto bg-theme-bg
        prose prose-slate dark:prose-invert max-w-none
        px-6 py-4
        ${className}
      `}
      style={{
        scrollBehavior: 'smooth'
      }}
    >
      {content.trim() ? (
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks]}
          components={{
          // 自定义代码块渲染
          code({ node, inline, className: codeClassName, children, ...props }: any) {
            const match = /language-(\w+)/.exec(codeClassName || '')
            const language = match ? match[1] : ''

            if (!inline && language) {
              return (
                <div className="my-4 rounded-lg overflow-hidden border border-theme-border">
                  <div className="flex items-center justify-between px-4 py-2 bg-theme-surface border-b border-theme-border">
                    <span className="text-xs font-mono text-theme-text-muted uppercase">
                      {language}
                    </span>
                    <button
                      onClick={() => navigator.clipboard.writeText(String(children).replace(/\n$/, ''))}
                      className="text-xs text-theme-text-muted hover:text-theme-text transition-colors"
                    >
                      复制
                    </button>
                  </div>
                  <SyntaxHighlighter
                    style={theme === 'dark' ? oneDark : oneLight}
                    language={language}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      background: 'transparent',
                      fontSize: '14px',
                      lineHeight: '1.5'
                    }}
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                </div>
              )
            }

            return (
              <code
                className="px-1.5 py-0.5 rounded bg-theme-surface text-theme-accent font-mono text-sm border border-theme-border"
                {...props}
              >
                {children}
              </code>
            )
          },
          // 自定义标题渲染
          h1: ({ children }) => (
            <h1 className="text-3xl font-bold text-theme-primary mb-4 pb-2 border-b border-theme-primary/20">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-2xl font-semibold text-theme-text mb-3 mt-6">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-xl font-medium text-theme-text mb-2 mt-4">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-lg font-medium text-theme-text mb-2 mt-3">
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 className="text-base font-medium text-theme-text mb-1 mt-2">
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 className="text-sm font-medium text-theme-text mb-1 mt-2">
              {children}
            </h6>
          ),
          // 自定义段落渲染
          p: ({ children }) => (
            <p className="text-theme-text leading-relaxed mb-4">
              {children}
            </p>
          ),
          // 自定义列表渲染
          ul: ({ children }) => (
            <ul className="list-disc list-inside text-theme-text mb-4 space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside text-theme-text mb-4 space-y-1">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-theme-text">
              {children}
            </li>
          ),
          // 自定义引用渲染
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-theme-primary pl-4 py-2 bg-theme-surface/30 rounded-r-lg mb-4 italic text-theme-text-secondary">
              {children}
            </blockquote>
          ),
          // 自定义链接渲染
          a: ({ href, children }) => (
            <a 
              href={href} 
              className="text-theme-primary hover:text-theme-highlight underline transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          // 自定义表格渲染
          table: ({ children }) => (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full border border-theme-primary/20 rounded-lg">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-theme-surface/50">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="divide-y divide-theme-primary/20">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="hover:bg-theme-surface/30 transition-colors">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-2 text-left text-theme-primary font-semibold border-b border-theme-primary/20">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-theme-text">
              {children}
            </td>
          ),
          // 自定义分割线渲染
          hr: () => (
            <hr className="border-theme-primary/20 my-6" />
          ),
          // 自定义强调文本渲染
          strong: ({ children }) => (
            <strong className="font-bold text-theme-primary">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="italic text-theme-text-secondary">
              {children}
            </em>
          )
          }}
        >
          {content}
        </ReactMarkdown>
      ) : (
        <div className="flex items-center justify-center h-full text-theme-text-muted">
          <div className="text-center">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-lg">开始编写你的Markdown内容</p>
            <p className="text-sm mt-2">切换到编辑模式开始创作</p>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default MarkdownPreview
