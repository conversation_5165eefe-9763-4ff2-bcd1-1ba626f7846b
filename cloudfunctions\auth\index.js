const cloudbase = require('@cloudbase/node-sdk')

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
})

const db = app.database()
const auth = app.auth()

/**
 * 用户认证云函数
 * 支持登录、注册、验证token等操作
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'login':
        return await handleLogin(data)
      case 'verify':
        return await handleVerify(data)
      case 'logout':
        return await handleLogout(data)
      case 'getUserInfo':
        return await handleGetUserInfo(data)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('认证错误:', error)
    return {
      success: false,
      message: error.message || '认证服务异常'
    }
  }
}

/**
 * 处理用户登录
 */
async function handleLogin({ username, password }) {
  if (!username || !password) {
    return {
      success: false,
      message: '用户名和密码不能为空'
    }
  }

  // 查询用户
  const userResult = await db.collection('users')
    .where({
      username: username,
      isActive: true
    })
    .get()

  if (userResult.data.length === 0) {
    return {
      success: false,
      message: '用户不存在或已被禁用'
    }
  }

  const user = userResult.data[0]

  // 简单的密码验证（实际项目中应该使用bcrypt等加密）
  if (password !== 'admin123') {
    return {
      success: false,
      message: '密码错误'
    }
  }

  // 更新最后登录时间
  await db.collection('users')
    .doc(user._id)
    .update({
      lastLoginAt: new Date().toISOString()
    })

  // 生成简单的token（实际项目中应该使用JWT）
  const token = Buffer.from(JSON.stringify({
    userId: user._id,
    username: user.username,
    role: user.role,
    timestamp: Date.now()
  })).toString('base64')

  return {
    success: true,
    message: '登录成功',
    data: {
      token: token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
        preferences: user.preferences,
        lastLoginAt: new Date().toISOString()
      }
    }
  }
}

/**
 * 验证token有效性
 */
async function handleVerify({ token }) {
  if (!token) {
    return {
      success: false,
      message: 'Token不能为空'
    }
  }

  try {
    // 验证简单token
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString())

    // 检查token是否过期（24小时）
    if (Date.now() - decoded.timestamp > 24 * 60 * 60 * 1000) {
      return {
        success: false,
        message: 'Token已过期'
      }
    }

    // 获取用户信息
    const userResult = await db.collection('users')
      .doc(decoded.userId)
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]

    return {
      success: true,
      message: 'Token验证成功',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          preferences: user.preferences
        }
      }
    }
  } catch (error) {
    return {
      success: false,
      message: 'Token无效或已过期'
    }
  }
}

/**
 * 处理用户登出
 */
async function handleLogout({ userId }) {
  // 这里可以添加登出日志记录等逻辑
  return {
    success: true,
    message: '登出成功'
  }
}

/**
 * 获取用户信息
 */
async function handleGetUserInfo({ userId }) {
  if (!userId) {
    return {
      success: false,
      message: '用户ID不能为空'
    }
  }

  const userResult = await db.collection('users')
    .doc(userId)
    .get()

  if (userResult.data.length === 0) {
    return {
      success: false,
      message: '用户不存在'
    }
  }

  const user = userResult.data[0]

  return {
    success: true,
    data: {
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
        preferences: user.preferences,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    }
  }
}
