import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, FileText, Folder, Hash } from 'lucide-react'
import { useNotesStore } from '../../stores/notesStore'
import { callFunction } from '../../utils/cloudbase'
import { useAuthStore } from '../../stores/authStore'

interface SearchResult {
  id: string
  title: string
  content: string
  type: 'note' | 'folder' | 'tag'
  category?: string
  updatedAt: string
}

interface SearchBarProps {
  className?: string
  onResultClick?: (result: SearchResult) => void
  onClose?: () => void
  autoFocus?: boolean
}

const SearchBar: React.FC<SearchBarProps> = ({
  className = '',
  onResultClick,
  onClose,
  autoFocus = false
}) => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  const { user } = useAuthStore()
  const { folders } = useNotesStore()

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  // 点击外部关闭搜索结果
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
        onClose?.()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  // 搜索函数
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim() || !user) {
      setResults([])
      return
    }

    setIsSearching(true)
    
    try {
      // 调用云函数搜索
      const result = await callFunction('notes', {
        action: 'search',
        data: {
          userId: user.id,
          keyword: searchQuery,
          limit: 10
        }
      })

      if (result?.success) {
        const searchResults: SearchResult[] = result.data.notes.map((note: any) => ({
          id: note._id,
          title: note.title,
          content: note.content.substring(0, 100) + '...',
          type: 'note' as const,
          category: note.category,
          updatedAt: note.updatedAt
        }))

        // 本地搜索文件夹
        const folderResults: SearchResult[] = folders
          .filter(folder => 
            folder.name.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map(folder => ({
            id: folder.id,
            title: folder.name,
            content: `包含 ${folder.noteIds.length} 个笔记`,
            type: 'folder' as const,
            updatedAt: folder.updatedAt
          }))

        setResults([...searchResults, ...folderResults])
      }
    } catch (error) {
      console.error('搜索失败:', error)
      setResults([])
    } finally {
      setIsSearching(false)
    }
  }

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      if (query) {
        performSearch(query)
        setShowResults(true)
      } else {
        setResults([])
        setShowResults(false)
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [query])

  const handleResultClick = (result: SearchResult) => {
    setShowResults(false)
    setQuery('')
    onResultClick?.(result)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setShowResults(false)
    inputRef.current?.focus()
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'note':
        return <FileText className="w-4 h-4" />
      case 'folder':
        return <Folder className="w-4 h-4" />
      case 'tag':
        return <Hash className="w-4 h-4" />
      default:
        return <Search className="w-4 h-4" />
    }
  }

  const getCategoryColor = (category?: string) => {
    const colors = {
      mercury: 'text-mercury-gray',
      venus: 'text-venus-cloud',
      earth: 'text-earth-blue',
      mars: 'text-mars-red',
      jupiter: 'text-jupiter-storm',
      saturn: 'text-saturn-ring'
    }
    return category ? colors[category as keyof typeof colors] || 'text-venus-cloud' : 'text-venus-cloud'
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* 搜索输入框 */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-sun-corona/60" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="在太阳系中导航..."
          className="w-full pl-10 pr-10 py-2 bg-cosmic-void/50 border border-sun-corona/20 rounded-full text-venus-cloud placeholder-venus-cloud/50 focus:border-sun-corona focus:outline-none transition-all duration-300"
        />
        
        {/* 清除按钮 */}
        {query && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-venus-cloud/60 hover:text-venus-cloud transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}

        {/* 加载指示器 */}
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <motion.div
              className="w-4 h-4 border-2 border-sun-corona border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          </div>
        )}
      </div>

      {/* 搜索结果 */}
      <AnimatePresence>
        {showResults && results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-starfield/95 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl shadow-cosmic max-h-96 overflow-y-auto z-50"
          >
            <div className="p-2">
              {results.map((result, index) => (
                <motion.button
                  key={result.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  onClick={() => handleResultClick(result)}
                  className="w-full flex items-start gap-3 p-3 rounded-lg hover:bg-sun-corona/10 transition-colors text-left"
                >
                  <div className={`mt-1 ${getCategoryColor(result.category)}`}>
                    {getResultIcon(result.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-venus-cloud font-medium truncate">
                      {result.title}
                    </h4>
                    <p className="text-venus-cloud/60 text-sm mt-1 line-clamp-2">
                      {result.content}
                    </p>
                    <div className="flex items-center gap-2 mt-2 text-xs text-venus-cloud/40">
                      <span className="capitalize">{result.type}</span>
                      <span>•</span>
                      <span>{new Date(result.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 无结果提示 */}
      <AnimatePresence>
        {showResults && query && !isSearching && results.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-starfield/95 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl shadow-cosmic p-6 text-center z-50"
          >
            <div className="text-venus-cloud/60">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>在太阳系中未找到相关内容</p>
              <p className="text-sm mt-1">尝试使用不同的关键词</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SearchBar
