#!/usr/bin/env node

/**
 * 立即执行的部署脚本
 * 这个脚本展示了如何在AI助手中使用MCP工具进行自动化部署
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 部署配置
const CONFIG = {
  envId: 'ai-demo-8gjoyg63e237ce06',
  localPath: './dist',
  cloudPath: 'cloud-notes',
  domain: 'ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com',
  
  // 需要清理的旧版本目录
  cleanupDirectories: [
    'solar-notes',
    'cloud-admin', 
    'notes-app',
    'admin-panel',
    'old-notes'
  ]
};

console.log('\n🚀 云开发静态托管自动化部署');
console.log('=====================================');
console.log(`环境ID: ${CONFIG.envId}`);
console.log(`部署路径: ${CONFIG.cloudPath}`);
console.log(`本地构建: ${CONFIG.localPath}`);
console.log('=====================================\n');

// 检查前置条件
console.log('📋 第1步：检查前置条件');
console.log('-------------------------------------');

if (!fs.existsSync(CONFIG.localPath)) {
  console.log(`❌ 构建目录不存在: ${CONFIG.localPath}`);
  console.log('请先运行: npm run build');
  process.exit(1);
}

const files = fs.readdirSync(CONFIG.localPath);
if (files.length === 0) {
  console.log(`❌ 构建目录为空: ${CONFIG.localPath}`);
  console.log('请先运行: npm run build');
  process.exit(1);
}

if (!fs.existsSync(path.join(CONFIG.localPath, 'index.html'))) {
  console.log('❌ 缺少关键文件: index.html');
  process.exit(1);
}

console.log('✅ 前置条件检查通过');
console.log(`   - 构建目录存在: ${CONFIG.localPath}`);
console.log(`   - 文件数量: ${files.length} 个`);
console.log(`   - 关键文件: index.html ✓`);

// 部署前清理
console.log('\n🧹 第2步：部署前清理');
console.log('-------------------------------------');
console.log('需要清理的旧版本目录:');

CONFIG.cleanupDirectories.forEach((dir, index) => {
  console.log(`   ${index + 1}. ${dir}`);
});

console.log('\n📝 MCP工具调用指令:');
console.log('请在AI助手中依次执行以下命令：\n');

CONFIG.cleanupDirectories.forEach((dir, index) => {
  console.log(`${index + 1}. deleteFiles({`);
  console.log(`     cloudPath: "${dir}",`);
  console.log(`     isDir: true`);
  console.log(`   })\n`);
});

// 部署新版本
console.log('📤 第3步：部署新版本');
console.log('-------------------------------------');

const localAbsPath = path.resolve(CONFIG.localPath);
console.log(`本地路径: ${localAbsPath}`);
console.log(`云端路径: ${CONFIG.cloudPath}`);

console.log('\n📝 MCP工具调用指令:');
console.log('请在AI助手中执行以下命令：\n');

console.log(`uploadFiles({`);
console.log(`  localPath: "${localAbsPath}",`);
console.log(`  cloudPath: "${CONFIG.cloudPath}"`);
console.log(`})\n`);

// 验证部署
console.log('🔍 第4步：验证部署');
console.log('-------------------------------------');

const accessUrl = `https://${CONFIG.domain}/${CONFIG.cloudPath}/`;
console.log(`访问地址: ${accessUrl}`);

console.log('\n📝 MCP工具调用指令（可选）:');
console.log('请在AI助手中执行以下命令验证：\n');

console.log(`findFiles({`);
console.log(`  prefix: "${CONFIG.cloudPath}/",`);
console.log(`  maxKeys: 10`);
console.log(`})\n`);

// 更新文档
console.log('📝 第5步：更新文档');
console.log('-------------------------------------');

const readmePath = path.join(process.cwd(), 'README.md');

if (fs.existsSync(readmePath)) {
  try {
    let content = fs.readFileSync(readmePath, 'utf8');
    const timestamp = new Date().toISOString().split('T')[0];
    
    // 更新访问地址
    const urlPattern = /\*\*正式环境：\*\* \[https:\/\/[^\]]+\]/g;
    const newUrlText = `**正式环境：** [${accessUrl}](${accessUrl})`;
    
    if (urlPattern.test(content)) {
      content = content.replace(urlPattern, newUrlText);
      console.log('✅ 已更新访问地址');
    }
    
    // 更新部署时间
    if (content.includes('最后部署')) {
      content = content.replace(
        /- \*\*最后部署\*\*：[^\n]+/g,
        `- **最后部署**：${timestamp}`
      );
      console.log('✅ 已更新部署时间');
    }
    
    fs.writeFileSync(readmePath, content, 'utf8');
    console.log('✅ README.md 更新完成');
    
  } catch (error) {
    console.log(`❌ 文档更新失败: ${error.message}`);
  }
} else {
  console.log('⚠️  README.md 不存在，跳过文档更新');
}

// 生成部署报告
console.log('\n📊 第6步：生成部署报告');
console.log('-------------------------------------');

const report = {
  timestamp: new Date().toISOString(),
  config: CONFIG,
  accessUrl: accessUrl,
  cleanupDirectories: CONFIG.cleanupDirectories.length,
  localFiles: files.length
};

const reportPath = path.join(process.cwd(), 'deploy-report.json');
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');

console.log(`✅ 部署报告已保存: ${reportPath}`);

// 部署摘要
console.log('\n🎉 部署准备完成!');
console.log('=====================================');
console.log(`✅ 访问地址: ${accessUrl}`);
console.log(`✅ 本地文件: ${files.length} 个`);
console.log(`✅ 清理目录: ${CONFIG.cleanupDirectories.length} 个`);
console.log(`✅ 部署时间: ${new Date().toLocaleString()}`);
console.log('=====================================\n');

console.log('📋 接下来的步骤:');
console.log('1. 在AI助手中依次执行上述MCP工具调用指令');
console.log('2. 等待所有操作完成');
console.log('3. 访问新的部署地址验证功能');
console.log('4. 检查旧版本目录是否已清理\n');

console.log('💡 提示:');
console.log('- CDN缓存可能需要几分钟刷新');
console.log('- 如有缓存问题，请强制刷新浏览器 (Ctrl+F5)');
console.log('- 文档已自动更新访问地址');
console.log('- 部署报告已保存到 deploy-report.json\n');

console.log('🔗 相关链接:');
console.log(`- 访问地址: ${accessUrl}`);
console.log('- 静态托管管理: https://console.cloud.tencent.com/tcb/hosting');
console.log('- 部署文档: docs/DEPLOYMENT.md\n');

// 输出MCP工具调用摘要
console.log('📋 MCP工具调用摘要:');
console.log('=====================================');
console.log('1. deleteFiles - 清理旧版本目录 (5次调用)');
console.log('2. uploadFiles - 上传新版本文件 (1次调用)');
console.log('3. findFiles - 验证部署结果 (1次调用，可选)');
console.log('=====================================\n');

console.log('🚀 准备就绪，请开始执行MCP工具调用！');
