import React, { useState, useRef, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-toastify'
import MonacoEditor from './MonacoEditor'
import MarkdownPreview from './MarkdownPreview'
import HtmlPreview from './HtmlPreview'
import EditorToolbar from './EditorToolbar'
import HtmlToolbar from './HtmlToolbar'
import { SaveStatus as SaveStatusType } from './SaveStatus'
import { useNotesStore } from '../../stores/notesStore'
import { useEditorMode } from '../../contexts/EditorModeContext'

interface EnhancedEditorProps {
  className?: string
}

const EnhancedEditor: React.FC<EnhancedEditorProps> = ({
  className = ''
}) => {
  const {
    getActiveTab,
    updateTabContent,
    saveTabToCloud,
    activeTabId
  } = useNotesStore()

  const activeTab = getActiveTab()
  const { editorMode: activeMode, setEditorMode: setActiveMode } = useEditorMode()
  const [saveStatus, setSaveStatus] = useState<SaveStatusType>('saved')

  const [editorKey, setEditorKey] = useState(0)

  const editorRef = useRef<any>(null)

  // 获取当前文件类型
  const getCurrentFileType = (): 'markdown' | 'html' => {
    if (!activeTab) return 'markdown'
    const extension = activeTab.title.split('.').pop()?.toLowerCase()
    return extension === 'html' || extension === 'htm' ? 'html' : 'markdown'
  }

  const currentFileType = getCurrentFileType()

  // 获取当前标签页的值，如果没有则使用默认值
  const value = activeTab?.content || ''
  const hasUnsavedChanges = activeTab?.hasUnsavedChanges || false

  // 根据文件扩展名自动检测语言
  const getLanguageFromFileName = useCallback((fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase()

    switch (extension) {
      case 'md':
      case 'markdown':
        return 'markdown'
      case 'html':
      case 'htm':
        return 'html'
      case 'js':
        return 'javascript'
      case 'ts':
        return 'typescript'
      case 'jsx':
        return 'javascript'
      case 'tsx':
        return 'typescript'
      case 'json':
        return 'json'
      case 'css':
        return 'css'
      case 'scss':
        return 'scss'
      case 'py':
        return 'python'
      case 'java':
        return 'java'
      case 'cpp':
      case 'c':
        return 'cpp'
      case 'go':
        return 'go'
      case 'rs':
        return 'rust'
      case 'php':
        return 'php'
      case 'rb':
        return 'ruby'
      case 'sql':
        return 'sql'
      case 'xml':
        return 'xml'
      case 'yaml':
      case 'yml':
        return 'yaml'
      default:
        return 'markdown'
    }
  }, [])

  const currentLanguage = activeTab ? getLanguageFromFileName(activeTab.title) : 'markdown'
  const isHtmlFile = currentLanguage === 'html'

  // 当活跃标签页改变时，强制重新渲染编辑器以确保内容同步
  useEffect(() => {
    setEditorKey(prev => prev + 1)
  }, [activeTabId])

  // 处理内容变化
  const handleContentChange = useCallback((newValue: string) => {
    if (activeTabId) {
      updateTabContent(activeTabId, newValue)
    }
  }, [activeTabId, updateTabContent])

  // 处理保存
  const handleSave = useCallback(async () => {
    if (!hasUnsavedChanges || !activeTab) {
      toast.info('没有需要保存的更改')
      return
    }

    setSaveStatus('saving')

    try {
      // 保存到云数据库
      await saveTabToCloud(activeTab.id)

      setSaveStatus('saved')
      toast.success('保存成功')
    } catch (error) {
      setSaveStatus('error')
      const message = error instanceof Error ? error.message : '保存失败'
      toast.error(message)
      console.error('保存失败:', error)
    }
  }, [activeTab, hasUnsavedChanges, saveTabToCloud])



  // 辅助函数
  const getSelectionInfo = useCallback(() => {
    // 简化的选择信息获取，实际应该从Monaco编辑器获取
    return { start: value.length, end: value.length }
  }, [value])

  const focusEditor = useCallback(() => {
    // 聚焦编辑器
    if (editorRef.current && typeof editorRef.current.focus === 'function') {
      editorRef.current.focus()
    }
  }, [])

  const insertTextAtCursor = useCallback((text: string, _cursorOffset: number = 0) => {
    // 这里需要通过ref调用Monaco编辑器的方法
    // 由于Monaco编辑器组件的限制，我们直接修改value
    const selection = getSelectionInfo()
    const beforeCursor = value.substring(0, selection.start)
    const afterCursor = value.substring(selection.end)
    const newValue = beforeCursor + text + afterCursor

    handleContentChange(newValue)

    // 设置新的光标位置（这里简化处理）
    setTimeout(() => {
      focusEditor()
    }, 50)
  }, [value, handleContentChange, getSelectionInfo, focusEditor])

  const wrapSelectedText = useCallback((prefix: string, suffix: string) => {
    const selection = getSelectionInfo()
    const selectedText = value.substring(selection.start, selection.end)
    const beforeSelection = value.substring(0, selection.start)
    const afterSelection = value.substring(selection.end)
    const newValue = beforeSelection + prefix + selectedText + suffix + afterSelection

    handleContentChange(newValue)

    setTimeout(() => {
      focusEditor()
    }, 50)
  }, [value, handleContentChange, getSelectionInfo, focusEditor])

  // 工具栏操作
  const handleInsertText = useCallback((text: string, cursorOffset: number = 0) => {
    if (activeMode !== 'edit') {
      setActiveMode('edit')
      // 等待模式切换完成后再插入文本
      setTimeout(() => {
        insertTextAtCursor(text, cursorOffset)
      }, 100)
    } else {
      insertTextAtCursor(text, cursorOffset)
    }
  }, [activeMode, insertTextAtCursor])

  const handleWrapText = useCallback((prefix: string, suffix: string) => {
    if (activeMode !== 'edit') {
      setActiveMode('edit')
      // 等待模式切换完成后再包装文本
      setTimeout(() => {
        wrapSelectedText(prefix, suffix)
      }, 100)
    } else {
      wrapSelectedText(prefix, suffix)
    }
  }, [activeMode, wrapSelectedText])

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S / Cmd+S 保存
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        handleSave()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleSave])

  // 如果没有活跃标签页，显示空状态
  if (!activeTab) {
    return (
      <div className={`flex flex-col h-full bg-theme-bg ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="w-20 h-20 bg-theme-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-theme-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-theme-text mb-3">
              选择一个笔记开始编辑
            </h3>
            <p className="text-theme-text-muted text-sm leading-relaxed">
              从左侧目录树中选择一个笔记，或创建一个新的笔记来开始您的创作之旅。
              享受多标签页编辑体验，让您的想法闪闪发光。
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full bg-theme-bg ${className}`}>
      {/* 编辑器工具栏 */}
      <AnimatePresence>
        {activeMode === 'edit' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {currentFileType === 'html' ? (
              <HtmlToolbar
                onInsertText={handleInsertText}
                onWrapText={handleWrapText}
                disabled={saveStatus === 'saving'}
              />
            ) : (
              <EditorToolbar
                onInsertText={handleInsertText}
                onWrapText={handleWrapText}
                disabled={saveStatus === 'saving'}
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* 编辑器内容区域 */}
      <div className="flex-1 relative overflow-hidden">
        <AnimatePresence mode="wait">
          {activeMode === 'edit' ? (
            <motion.div
              key="editor"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="absolute inset-0"
            >
              <MonacoEditor
                key={editorKey}
                ref={editorRef}
                value={value}
                onChange={handleContentChange}
                onSave={handleSave}

                readOnly={saveStatus === 'saving'}
                language={currentLanguage}
                height="100%"
                options={{
                  automaticLayout: true,
                  minimap: { enabled: false },
                  fontSize: 14,
                  lineHeight: 1.6,
                  wordWrap: 'on',
                  scrollBeyondLastLine: false,
                  padding: { top: 16, bottom: 16 },
                  lineNumbers: 'on',
                  folding: true,
                  foldingStrategy: 'auto',
                  showFoldingControls: 'mouseover',
                  renderLineHighlight: 'line',
                  cursorBlinking: 'smooth',
                  cursorSmoothCaretAnimation: 'on',
                  smoothScrolling: true,
                  mouseWheelScrollSensitivity: 1,
                  fastScrollSensitivity: 5,
                  scrollbar: {
                    vertical: 'auto',
                    horizontal: 'auto',
                    useShadows: false,
                    verticalHasArrows: false,
                    horizontalHasArrows: false,
                    verticalScrollbarSize: 10,
                    horizontalScrollbarSize: 10
                  }
                }}
              />
            </motion.div>
          ) : (
            <motion.div
              key="preview"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.2 }}
              className="absolute inset-0"
            >
              {isHtmlFile ? (
                <HtmlPreview
                  content={value}
                  className="h-full"
                />
              ) : (
                <MarkdownPreview
                  content={value}
                  className="h-full"
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default EnhancedEditor
