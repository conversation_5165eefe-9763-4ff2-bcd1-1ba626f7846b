/* 太阳系加载器样式 */
.solar-loader {
  position: relative;
  width: 100px;
  height: 100px;
}

.solar-loader .sun {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, var(--sun-core) 0%, var(--sun-corona) 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 40px var(--sun-corona);
}

.solar-loader .orbit {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.solar-loader .orbit-1 { 
  width: 60px; 
  height: 60px; 
  animation: orbit-rotation 3s linear infinite;
}

.solar-loader .orbit-2 { 
  width: 80px; 
  height: 80px; 
  animation: orbit-rotation 4s linear infinite reverse;
}

.solar-loader .orbit-3 { 
  width: 100px; 
  height: 100px; 
  animation: orbit-rotation 5s linear infinite;
}

/* 太阳系主题组件样式 */
.solar-button {
  position: relative;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--sun-corona) 0%, var(--jupiter-storm) 100%);
  border: none;
  border-radius: 50px;
  color: var(--cosmic-void);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.solar-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.solar-button:hover::before {
  left: 100%;
}

.solar-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(253, 184, 19, 0.4);
}

.solar-input {
  width: 100%;
  padding: 0.875rem 1rem;
  background: rgba(26, 26, 46, 0.8);
  border: 2px solid rgba(253, 184, 19, 0.2);
  border-radius: 12px;
  color: var(--venus-cloud);
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.solar-input:focus {
  outline: none;
  border-color: var(--sun-corona);
  box-shadow: 
    inset 0 0 20px rgba(253, 184, 19, 0.1),
    0 0 30px rgba(253, 184, 19, 0.2);
}

.solar-input::placeholder {
  color: rgba(255, 248, 220, 0.5);
}

/* 行星导航样式 */
.planet-nav {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: var(--venus-cloud);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.planet-nav::before {
  content: '';
  position: absolute;
  left: -100%;
  top: 0;
  bottom: 0;
  width: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(253, 184, 19, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.5s;
}

.planet-nav:hover::before {
  left: 100%;
}

.planet-nav:hover,
.planet-nav.active {
  color: var(--sun-corona);
  background: rgba(253, 184, 19, 0.05);
}

/* 轨道指示器 */
.orbit-indicator {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--sun-corona);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s;
}

.planet-nav.active .orbit-indicator {
  opacity: 1;
  animation: stellar-twinkle 2s ease-in-out infinite;
}

/* 彗星效果 */
.comet-effect {
  position: relative;
  overflow: hidden;
}

.comet-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--comet-tail) 50%,
    var(--sun-corona) 100%
  );
  transform: translateY(-50%);
  animation: comet-fly 3s ease-in-out infinite;
}

/* 星空背景 */
.starfield-bg {
  position: relative;
  background: var(--cosmic-void);
}

.starfield-bg::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: stellar-twinkle 4s ease-in-out infinite alternate;
  opacity: 0.3;
  pointer-events: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .solar-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .solar-input {
    padding: 0.75rem 0.875rem;
    font-size: 0.875rem;
  }
  
  .planet-nav {
    padding: 0.625rem 0.875rem;
    gap: 0.75rem;
  }
}
