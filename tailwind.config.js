/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 基础色彩
        'theme-bg': 'var(--theme-bg)',
        'theme-bg-secondary': 'var(--theme-bg-secondary)',
        'theme-bg-tertiary': 'var(--theme-bg-tertiary)',
        'theme-surface': 'var(--theme-surface)',
        'theme-surface-hover': 'var(--theme-surface-hover)',

        // 边框色彩
        'theme-border': 'var(--theme-border)',
        'theme-border-muted': 'var(--theme-border-muted)',
        'theme-border-subtle': 'var(--theme-border-subtle)',

        // 文本色彩
        'theme-text': 'var(--theme-text)',
        'theme-text-muted': 'var(--theme-text-muted)',
        'theme-text-subtle': 'var(--theme-text-subtle)',

        // 主色调
        'theme-primary': 'var(--theme-primary)',
        'theme-primary-hover': 'var(--theme-primary-hover)',
        'theme-primary-muted': 'var(--theme-primary-muted)',

        // 次要色调
        'theme-secondary': 'var(--theme-secondary)',
        'theme-secondary-hover': 'var(--theme-secondary-hover)',
        'theme-secondary-muted': 'var(--theme-secondary-muted)',

        // 状态色彩
        'theme-success': 'var(--theme-success)',
        'theme-success-hover': 'var(--theme-success-hover)',
        'theme-success-muted': 'var(--theme-success-muted)',
        'theme-warning': 'var(--theme-warning)',
        'theme-warning-hover': 'var(--theme-warning-hover)',
        'theme-warning-muted': 'var(--theme-warning-muted)',
        'theme-danger': 'var(--theme-danger)',
        'theme-danger-hover': 'var(--theme-danger-hover)',
        'theme-danger-muted': 'var(--theme-danger-muted)',

        // 特殊效果
        'theme-backdrop': 'var(--theme-backdrop)',
        'theme-overlay': 'var(--theme-overlay)',

        // 编辑器相关
        'theme-editor-bg': 'var(--theme-editor-bg)',
        'theme-editor-selection': 'var(--theme-editor-selection)',
        'theme-editor-line-highlight': 'var(--theme-editor-line-highlight)',

        // 搜索高亮
        'theme-search-highlight-bg': 'var(--theme-search-highlight-bg)',
        'theme-search-highlight-text': 'var(--theme-search-highlight-text)',
      },
      fontFamily: {
        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
      },
      fontWeight: {
        'theme-normal': 'var(--theme-font-weight-normal)',
        'theme-medium': 'var(--theme-font-weight-medium)',
        'theme-bold': 'var(--theme-font-weight-bold)',
      },
      lineHeight: {
        'theme-normal': 'var(--theme-line-height-normal)',
        'theme-tight': 'var(--theme-line-height-tight)',
      },
      letterSpacing: {
        'theme': 'var(--theme-letter-spacing)',
      },
      boxShadow: {
        'theme-sm': 'var(--theme-shadow-sm)',
        'theme': 'var(--theme-shadow)',
        'theme-md': 'var(--theme-shadow-md)',
        'theme-lg': 'var(--theme-shadow-lg)',
        'theme-xl': 'var(--theme-shadow-xl)',
      },
      transitionDuration: {
        'theme-fast': 'var(--theme-transition-fast)',
        'theme-normal': 'var(--theme-transition-normal)',
      },
      boxShadow: {
        'theme-sm': 'var(--theme-shadow-sm)',
        'theme': 'var(--theme-shadow)',
        'theme-md': 'var(--theme-shadow-md)',
        'theme-lg': 'var(--theme-shadow-lg)',
      }
    },
  },
  plugins: [],
}
