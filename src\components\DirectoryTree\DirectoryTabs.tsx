import React from 'react'
import { motion } from 'framer-motion'
import { ClockIcon, ShareIcon } from '../Icons/IconLibrary'
import { useDirectoryStore } from '../../stores/directoryStore'
import { DirectoryItem } from '../../services/directoryService'

export type DirectoryTabType = 'recent' | 'shared'

interface DirectoryTabsProps {
  activeTab: DirectoryTabType
  onSelectNote: (noteId: string) => void
  selectedNoteId?: string
}

const DirectoryTabs: React.FC<DirectoryTabsProps> = ({
  activeTab,
  onSelectNote,
  selectedNoteId
}) => {
  const { recentFiles, sharedFiles } = useDirectoryStore()

  // 当前显示的文件列表
  const currentFiles = activeTab === 'recent' ? recentFiles : sharedFiles

  // 格式化时间显示
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }

  // 获取文件类型图标
  const getFileIcon = (item: DirectoryItem) => {
    return item.type === 'html' ? '🌐' : '📝'
  }



  // 渲染文件项
  const renderFileItem = (item: DirectoryItem) => (
    <motion.div
      key={item._id}
      className={`
        flex items-center gap-3 px-3 py-2 rounded-md cursor-pointer
        transition-all duration-200 ease-out
        ${selectedNoteId === item._id
          ? 'bg-theme-primary/10 text-theme-primary'
          : 'hover:bg-theme-surface-hover text-theme-text'
        }
      `}
      onClick={() => onSelectNote(item._id)}
      whileHover={{ x: 2 }}
      whileTap={{ scale: 0.98 }}
    >
      {/* 文件类型图标 */}
      <span className="text-sm flex-shrink-0">
        {getFileIcon(item)}
      </span>

      {/* 文件信息 */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">
          {item.name}
        </div>
        <div className="text-xs text-theme-text-muted">
          {activeTab === 'recent' ? formatTime(item.updatedAt) : '已分享'}
        </div>
      </div>

      {/* 分享状态指示器 */}
      {activeTab === 'shared' && item.isShared && (
        <div className="flex-shrink-0">
          <span className="text-xs text-theme-success" title="已分享">
            ✏️
          </span>
        </div>
      )}
    </motion.div>
  )

  return (
    <div className="flex-1 overflow-auto">
        <div className="p-3">
          {currentFiles.length > 0 ? (
            <div className="space-y-1">
              {currentFiles.map(renderFileItem)}
            </div>
          ) : (
            /* 空状态 */
            <div className="px-2 py-8 text-center">
              <div className="w-12 h-12 bg-theme-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                {activeTab === 'recent' ? (
                  <ClockIcon className="icon-xl text-theme-primary/50" />
                ) : (
                  <ShareIcon className="icon-xl text-theme-primary/50" />
                )}
              </div>
              <h3 className="text-sm font-medium text-theme-text mb-1">
                {activeTab === 'recent' ? '暂无最近文件' : '暂无分享文件'}
              </h3>
              <p className="text-xs text-theme-text-muted">
                {activeTab === 'recent' 
                  ? '开始编辑笔记后会在这里显示' 
                  : '分享笔记后会在这里显示'
                }
              </p>
            </div>
          )}
        </div>
    </div>
  )
}

export default DirectoryTabs
