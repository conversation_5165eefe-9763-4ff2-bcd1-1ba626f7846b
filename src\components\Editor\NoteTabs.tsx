import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, FileText } from 'lucide-react'
import { useNotesStore } from '../../stores/notesStore'

interface NoteTabsProps {
  className?: string
}

const NoteTabs: React.FC<NoteTabsProps> = ({ className = '' }) => {
  const { 
    openTabs, 
    activeTabId, 
    setActiveTab, 
    closeTab 
  } = useNotesStore()

  if (openTabs.length === 0) {
    return null
  }

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId)
  }

  const handleCloseTab = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation()
    closeTab(tabId)
  }

  const handleCloseTabKeyboard = (e: React.KeyboardEvent, tabId: string) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      e.stopPropagation()
      closeTab(tabId)
    }
  }

  return (
    <div className={`flex items-center bg-transparent overflow-x-auto ${className}`}>
      <div className="flex items-center min-w-0">
        <AnimatePresence mode="popLayout">
          {openTabs.map((tab) => {
            const isActive = activeTabId === tab.id
            
            return (
              <motion.div
                key={tab.id}
                layout
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: -20 }}
                transition={{ 
                  type: "spring", 
                  bounce: 0.2, 
                  duration: 0.4,
                  layout: { duration: 0.2 }
                }}
                className="relative flex-shrink-0"
              >
                <button
                  onClick={() => handleTabClick(tab.id)}
                  className={`
                    group relative flex items-center gap-2 px-4 py-2 text-sm font-medium
                    transition-all duration-200 ease-out min-w-0 max-w-48
                    border-r border-theme-border/30 rounded-t-lg mx-0.5
                    ${isActive
                      ? 'text-theme-text bg-theme-bg shadow-sm border-b-2 border-b-theme-primary border-l border-theme-border border-r-theme-border border-t border-theme-border'
                      : 'text-theme-text-muted hover:text-theme-text hover:bg-theme-surface-hover/50'
                    }
                    focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-1 focus:ring-offset-theme-surface
                  `}
                  title={tab.title}
                >
                  {/* 文件图标 */}
                  <FileText className={`icon-sm flex-shrink-0 ${isActive ? 'text-theme-primary' : 'text-current'}`} />
                  
                  {/* 标题 */}
                  <span className="truncate flex-1 text-left">
                    {tab.title}
                  </span>
                  
                  {/* 未保存指示器 */}
                  {tab.hasUnsavedChanges && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-2 h-2 bg-theme-warning rounded-full flex-shrink-0"
                      title="有未保存的更改"
                    />
                  )}
                  
                  {/* 关闭按钮 */}
                  <motion.button
                    onClick={(e) => handleCloseTab(e, tab.id)}
                    onKeyDown={(e) => handleCloseTabKeyboard(e, tab.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`
                      flex-shrink-0 p-1 rounded-md transition-all duration-150
                      ${isActive 
                        ? 'text-theme-text-muted hover:text-theme-text hover:bg-theme-surface-hover' 
                        : 'text-theme-text-subtle hover:text-theme-text-muted hover:bg-theme-surface-hover'
                      }
                      opacity-0 group-hover:opacity-100 focus:opacity-100
                      focus:outline-none focus:ring-1 focus:ring-theme-primary
                    `}
                    title="关闭标签页"
                    tabIndex={-1}
                  >
                    <X className="w-3 h-3" />
                  </motion.button>
                  
                  {/* 活动状态指示器 */}
                  {isActive && (
                    <motion.div
                      layoutId="activeNoteTab"
                      className="absolute inset-0 bg-theme-primary/5 rounded-t-md pointer-events-none"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default NoteTabs
