import React from 'react'
import { useParams } from 'react-router-dom'

const ShareView: React.FC = () => {
  const { shareId } = useParams<{ shareId: string }>()

  return (
    <div className="min-h-screen bg-cosmic-void starfield-bg p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-starfield/80 backdrop-blur-cosmic border border-sun-corona/20 rounded-xl p-8">
          <h1 className="text-2xl font-bold text-sun-corona mb-6">
            共享笔记查看
          </h1>
          
          <div className="bg-jupiter-storm/20 border border-jupiter-storm/40 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-jupiter-storm mb-2">
              🚧 功能开发中
            </h3>
            <p className="text-venus-cloud/80 mb-4">
              分享功能正在开发中，将支持：
            </p>
            <ul className="list-disc list-inside text-venus-cloud/80 space-y-1">
              <li>通过分享链接访问笔记</li>
              <li>密码保护的分享</li>
              <li>分享有效期控制</li>
              <li>只读/可编辑权限设置</li>
            </ul>
            <p className="text-venus-cloud/60 mt-4 text-sm">
              分享ID: {shareId}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShareView
