import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { callFunction } from '../utils/cloudbase'

interface User {
  id: string
  username: string
  email?: string
  role: 'admin' | 'user'
  avatar?: string
  createdAt: string
  lastLoginAt: string
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  token: string | null
  
  // Actions
  login: (credentials: { username: string; password: string }) => Promise<boolean>
  logout: () => void
  setUser: (user: User) => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<boolean>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: true,
      token: null,

      login: async (credentials) => {
        set({ isLoading: true })

        try {
          // 先进行匿名登录以获取调用云函数的权限
          const { authService } = await import('../utils/cloudbase')
          await authService.signInAnonymously()

          const result = await callFunction('auth', {
            action: 'login',
            data: credentials
          })

          if (result?.success) {
            const { token, user } = result.data

            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              token
            })

            return true
          } else {
            set({ isLoading: false })
            console.error('Login failed:', result?.message)
            return false
          }
        } catch (error) {
          console.error('Login error:', error)
          set({ isLoading: false })
          return false
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          token: null,
          isLoading: false
        })
      },

      setUser: (user) => {
        set({ user, isAuthenticated: true })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      checkAuth: async () => {
        const { token } = get()

        if (!token) {
          set({ isLoading: false, isAuthenticated: false })
          return false
        }

        try {
          const result = await callFunction('auth', {
            action: 'verify',
            data: { token }
          })

          if (result?.success) {
            set({
              isLoading: false,
              user: result.data.user,
              isAuthenticated: true
            })
            return true
          } else {
            set({
              isLoading: false,
              isAuthenticated: false,
              user: null,
              token: null
            })
            return false
          }
        } catch (error) {
          console.error('Auth check error:', error)
          set({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null
          })
          return false
        }
      }
    }),
    {
      name: 'solar-notes-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
