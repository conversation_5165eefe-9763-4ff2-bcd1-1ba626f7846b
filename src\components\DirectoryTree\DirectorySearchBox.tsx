import React, { useState, useRef, useEffect } from 'react'
import { SearchIcon, XIcon } from '../Icons/IconLibrary'

interface DirectorySearchBoxProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  onFocus?: () => void
  onBlur?: () => void
}

const DirectorySearchBox: React.FC<DirectorySearchBoxProps> = ({
  value,
  onChange,
  placeholder = "搜索笔记...",
  className = "",
  onFocus,
  onBlur
}) => {
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+F 或 Cmd+F 聚焦搜索框
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault()
        inputRef.current?.focus()
      }
      
      // ESC 清空搜索并失焦
      if (e.key === 'Escape' && isFocused) {
        onChange('')
        inputRef.current?.blur()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFocused, onChange])

  const handleFocus = () => {
    setIsFocused(true)
    onFocus?.()
  }

  const handleBlur = () => {
    setIsFocused(false)
    onBlur?.()
  }

  const handleClear = () => {
    onChange('')
    inputRef.current?.focus()
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`relative flex items-center transition-all duration-200 ${
          isFocused
            ? 'bg-theme-primary/20 border-theme-primary/50'
            : 'bg-theme-primary/10 border-theme-primary/20 hover:bg-theme-primary/15'
        } border rounded-lg px-2 py-1.5`}
      >
        {/* 搜索图标 */}
        <div className="flex items-center justify-center flex-shrink-0 mr-2">
          <SearchIcon
            className={`icon-xs transition-colors ${
              isFocused ? 'text-theme-primary' : 'text-theme-text-muted'
            }`}
          />
        </div>

        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className="flex-1 text-sm bg-transparent text-theme-text placeholder-theme-text-muted focus:outline-none"
        />

        {/* 清空按钮 */}
        {value && (
          <button
            onClick={handleClear}
            className="flex items-center justify-center flex-shrink-0 ml-1 p-0.5 hover:bg-theme-primary/20 rounded transition-colors"
            title="清空搜索"
          >
            <XIcon className="icon-xs text-theme-text-muted hover:text-theme-text" />
          </button>
        )}
      </div>

      {/* 搜索提示 */}
      {isFocused && !value && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-theme-surface/95 backdrop-blur-lg border border-theme-primary/20 rounded-lg shadow-lg z-50">
          <div className="text-xs text-theme-text-muted space-y-1">
            <div className="flex items-center gap-2">
              <kbd className="px-1.5 py-0.5 bg-theme-primary/10 text-theme-primary rounded text-xs font-theme-normal">
                Ctrl+F
              </kbd>
              <span>快速聚焦搜索</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-1.5 py-0.5 bg-theme-primary/10 text-theme-primary rounded text-xs font-theme-normal">
                ESC
              </kbd>
              <span>清空并退出搜索</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DirectorySearchBox
