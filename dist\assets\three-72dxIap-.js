import{r as S,a as C,g as k,R as z}from"./vendor-CrAnlijj.js";var O={exports:{}},y={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W=S,A=Symbol.for("react.element"),F=Symbol.for("react.fragment"),L=Object.prototype.hasOwnProperty,U=W.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,M={key:!0,ref:!0,__self:!0,__source:!0};function D(e,t,r){var o,n={},u=null,i=null;r!==void 0&&(u=""+r),t.key!==void 0&&(u=""+t.key),t.ref!==void 0&&(i=t.ref);for(o in t)L.call(t,o)&&!M.hasOwnProperty(o)&&(n[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps,t)n[o]===void 0&&(n[o]=t[o]);return{$$typeof:A,type:e,key:u,ref:i,props:n,_owner:U.current}}y.Fragment=F;y.jsx=D;y.jsxs=D;O.exports=y;var ye=O.exports,g={},h=C;g.createRoot=h.createRoot,g.hydrateRoot=h.hydrateRoot;const N={},R=e=>{let t;const r=new Set,o=(s,l)=>{const c=typeof s=="function"?s(t):s;if(!Object.is(c,t)){const a=t;t=l??(typeof c!="object"||c===null)?c:Object.assign({},t,c),r.forEach(f=>f(t,a))}},n=()=>t,p={setState:o,getState:n,getInitialState:()=>m,subscribe:s=>(r.add(s),()=>r.delete(s)),destroy:()=>{(N?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},m=t=e(o,n,p);return p},B=e=>e?R(e):R;var $={exports:{}},I={},P={exports:{}},T={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var d=S;function G(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var J=typeof Object.is=="function"?Object.is:G,Y=d.useState,H=d.useEffect,K=d.useLayoutEffect,Q=d.useDebugValue;function X(e,t){var r=t(),o=Y({inst:{value:r,getSnapshot:t}}),n=o[0].inst,u=o[1];return K(function(){n.value=r,n.getSnapshot=t,b(n)&&u({inst:n})},[e,r,t]),H(function(){return b(n)&&u({inst:n}),e(function(){b(n)&&u({inst:n})})},[e]),Q(r),r}function b(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!J(e,r)}catch{return!0}}function Z(e,t){return t()}var q=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Z:X;T.useSyncExternalStore=d.useSyncExternalStore!==void 0?d.useSyncExternalStore:q;P.exports=T;var ee=P.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var E=S,te=ee;function re(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var oe=typeof Object.is=="function"?Object.is:re,ne=te.useSyncExternalStore,ue=E.useRef,se=E.useEffect,ae=E.useMemo,ie=E.useDebugValue;I.useSyncExternalStoreWithSelector=function(e,t,r,o,n){var u=ue(null);if(u.current===null){var i={hasValue:!1,value:null};u.current=i}else i=u.current;u=ae(function(){function p(a){if(!m){if(m=!0,s=a,a=o(a),n!==void 0&&i.hasValue){var f=i.value;if(n(f,a))return l=f}return l=a}if(f=l,oe(s,a))return f;var _=o(a);return n!==void 0&&n(f,_)?(s=a,f):(s=a,l=_)}var m=!1,s,l,c=r===void 0?null:r;return[function(){return p(t())},c===null?void 0:function(){return p(c())}]},[t,r,o,n]);var v=ne(e,u[0],u[1]);return se(function(){i.hasValue=!0,i.value=v},[v]),ie(v),v};$.exports=I;var ce=$.exports;const fe=k(ce),V={},{useDebugValue:le}=z,{useSyncExternalStoreWithSelector:de}=fe;let w=!1;const ve=e=>e;function pe(e,t=ve,r){(V?"production":void 0)!=="production"&&r&&!w&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),w=!0);const o=de(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return le(o),o}const x=e=>{(V?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?B(e):e,r=(o,n)=>pe(t,o,n);return Object.assign(r,t),r},Ee=e=>e?x(e):x;function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)({}).hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},j.apply(null,arguments)}export{j as _,g as a,Ee as c,ye as j};
