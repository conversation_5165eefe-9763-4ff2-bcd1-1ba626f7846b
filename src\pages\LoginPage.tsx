import React, { useState } from 'react'
import { Navigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { CloudNotesIcon, LockIcon, UserIcon } from '../components/Icons/IconLibrary'
import { useAuthStore } from '../stores/authStore'
import { toast } from 'react-toastify'

const LoginPage: React.FC = () => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  
  const { login, isAuthenticated } = useAuthStore()

  // 如果已登录，重定向到仪表板
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!credentials.username || !credentials.password) {
      toast.error('请输入用户名和密码')
      return
    }

    setIsLoading(true)
    
    try {
      const success = await login(credentials)
      
      if (success) {
        toast.success('欢迎回到太阳系！')
      } else {
        toast.error('登录失败，请检查用户名和密码')
      }
    } catch (error) {
      toast.error('登录过程中发生错误')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="min-h-screen bg-theme-bg flex items-center justify-center p-4">
      {/* 登录表单 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="bg-theme-surface border border-theme-border rounded-2xl p-8 shadow-theme-lg">
          {/* 标题 */}
          <div className="text-center mb-8">
            <div className="inline-block mb-4 p-3 bg-theme-primary/10 rounded-full">
              <CloudNotesIcon className="icon-xl text-theme-primary" />
            </div>
            <h1 className="text-3xl font-theme-bold text-theme-text mb-2">
              云笔记
            </h1>
            <p className="text-theme-text-muted">
              欢迎使用云笔记管理系统
            </p>
          </div>

          {/* 登录表单 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserIcon className="icon-lg text-theme-text-muted" />
                </div>
                <input
                  type="text"
                  placeholder="管理员用户名"
                  value={credentials.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  disabled={isLoading}
                  className="w-full pl-10 pr-4 py-3 bg-theme-bg border border-theme-border rounded-lg text-theme-text placeholder-theme-text-muted focus:outline-none focus:border-theme-primary focus:ring-1 focus:ring-theme-primary font-theme-normal"
                />
              </div>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockIcon className="icon-lg text-theme-text-muted" />
                </div>
                <input
                  type="password"
                  placeholder="访问密码"
                  value={credentials.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  disabled={isLoading}
                  className="w-full pl-10 pr-4 py-3 bg-theme-bg border border-theme-border rounded-lg text-theme-text placeholder-theme-text-muted focus:outline-none focus:border-theme-primary focus:ring-1 focus:ring-theme-primary font-theme-normal"
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 px-4 bg-theme-primary hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed text-white font-theme-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2 focus:ring-offset-theme-surface"
            >
              {isLoading ? '登录中...' : '登录'}
            </button>
          </form>

          {/* 提示信息 */}
          <div className="mt-6 text-center">
            <p className="text-theme-text-muted text-sm font-theme-normal">
              默认管理员账号: admin / admin123
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default LoginPage
