# 太阳系AI云笔记 - 完整版

一个基于腾讯云开发的现代化云笔记应用，支持完整的目录树增删改查功能。

## 🌟 项目特色

### 核心功能
- **完整的目录树CRUD操作**：支持文件夹、Markdown笔记、HTML文件的创建、读取、更新、删除
- **强大的文件编辑功能**：Monaco编辑器支持Markdown和HTML文件的专业编辑
- **回收站系统**：完整的软删除和恢复机制，支持数据安全保护
- **三标签视图**：全部、最近、分享三种文件组织方式
- **实时数据同步**：基于云数据库的实时数据存储和同步
- **智能搜索**：支持文件名搜索和高亮显示
- **右键菜单**：直观的右键操作菜单

### 技术架构
- **前端**：React 18 + TypeScript + Tailwind CSS + Framer Motion
- **后端**：腾讯云开发 CloudBase
- **数据库**：云数据库 MongoDB
- **云函数**：Node.js 18.15
- **部署**：静态网站托管

## 🚀 部署信息

### 线上访问地址
**正式环境：** [https://ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com/cloud-notes/](https://ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com/cloud-notes/)(https://ai-demo-8gjoyg63e237ce06-1252411375.tcloudbaseapp.com/cloud-notes/)

### 云开发环境
- **环境ID**：ai-demo-8gjoyg63e237ce06
- **环境别名**：ai-demo
- **地域**：上海（ap-shanghai）
- **最后部署**：2025-07-28
- **部署策略**：单版本自动清理

### 管理控制台
- **云函数管理**：[https://tcb.cloud.tencent.com/dev?envId=ai-demo-8gjoyg63e237ce06#/scf/detail?id=directoryTree&NameSpace=ai-demo-8gjoyg63e237ce06](https://tcb.cloud.tencent.com/dev?envId=ai-demo-8gjoyg63e237ce06#/scf/detail?id=directoryTree&NameSpace=ai-demo-8gjoyg63e237ce06)
- **数据库管理**：[https://tcb.cloud.tencent.com/dev?envId=ai-demo-8gjoyg63e237ce06#/db/doc/collection/directory_tree](https://tcb.cloud.tencent.com/dev?envId=ai-demo-8gjoyg63e237ce06#/db/doc/collection/directory_tree)
- **静态托管**：[https://console.cloud.tencent.com/tcb/hosting](https://console.cloud.tencent.com/tcb/hosting)

## 🎯 功能特性

### 目录树操作
- ✅ 创建文件夹、Markdown笔记、HTML文件
- ✅ 重命名文件/文件夹（内联编辑）
- ✅ 软删除文件/文件夹（移到回收站 + 确认提示）
- ✅ 展开/折叠文件夹（支持多层级）
- ✅ 层级缩进显示

### 右键菜单增强
- ✅ **上下文感知**：根据右键位置智能显示菜单选项
- ✅ **空白区域右键**：在根目录下创建新文件/文件夹
- ✅ **文件夹右键**：在该文件夹内创建子文件/文件夹
- ✅ **多层级支持**：支持无限层级的文件夹嵌套
- ✅ **智能定位**：菜单位置自动调整，避免超出屏幕

### 展开/折叠功能
- ✅ **交互优化**：点击文件夹图标或名称切换展开状态
- ✅ **视觉指示**：清晰的箭头图标显示当前状态
- ✅ **状态持久化**：刷新页面后记住所有文件夹的展开状态
- ✅ **多层级独立**：每个层级的展开/折叠状态独立管理
- ✅ **自动展开**：创建子项目后父文件夹自动展开显示
- ✅ **流畅动画**：展开/折叠过程有自然的过渡效果

### 文件编辑功能
- ✅ **Monaco编辑器**：基于VS Code的专业代码编辑器，支持语法高亮
- ✅ **多文件支持**：同时编辑多个Markdown和HTML文件
- ✅ **标签页管理**：智能标签页系统，支持文件切换和状态跟踪
- ✅ **实时预览**：Markdown文件支持编辑/预览模式切换
- ✅ **语法高亮**：Markdown和HTML代码的专业语法高亮
- ✅ **工具栏支持**：丰富的编辑工具栏，快速插入常用元素
- ✅ **快捷键支持**：Ctrl+S保存、Ctrl+B加粗、Ctrl+I斜体等
- ✅ **自动保存检测**：智能检测未保存更改，防止数据丢失
- ✅ **云端同步**：编辑内容实时保存到云数据库

### 回收站功能
- ✅ **软删除机制**：删除操作移到回收站，数据可恢复
- ✅ **回收站UI**：左侧目录树底部显示回收站入口和文件数量
- ✅ **展开/折叠**：支持展开查看回收站内容
- ✅ **恢复功能**：支持将文件/文件夹恢复到原位置
- ✅ **永久删除**：提供彻底删除选项（不可恢复）
- ✅ **清空回收站**：批量永久删除所有回收站内容
- ✅ **删除信息**：显示删除时间和原路径信息

### 视图模式
- ✅ **全部**：完整目录树结构
- ✅ **最近**：按更新时间排序的最近文件
- ✅ **分享**：已共享的文件列表

### 交互体验
- ✅ 右键菜单操作
- ✅ 搜索高亮显示
- ✅ 加载状态指示
- ✅ 错误处理和重试
- ✅ 响应式设计

## 🧪 测试验证

### 自动化测试通过
使用Playwright MCP Server进行了全面的端到端测试：

- ✅ **认证功能**：CloudBase SDK匿名登录正常
- ✅ **目录树加载**：从云数据库正确加载目录结构
- ✅ **标签页切换**：全部、最近、分享三个标签正常切换
- ✅ **文件编辑功能**：Markdown和HTML文件编辑功能完全正常
- ✅ **多标签页管理**：同时编辑多个文件，状态跟踪正确
- ✅ **编辑器功能**：Monaco编辑器语法高亮、工具栏功能正常
- ✅ **预览功能**：Markdown和HTML文件预览渲染正确
- ✅ **保存功能**：快捷键保存、云端同步、状态检测正常
- ✅ **右键菜单增强**：上下文感知的智能右键菜单功能正常
- ✅ **多层级创建**：空白区域和文件夹内创建子项目功能正常
- ✅ **重命名功能**：内联编辑重命名功能正常
- ✅ **软删除功能**：带确认对话框的移到回收站功能正常
- ✅ **回收站功能**：展开/折叠、恢复、永久删除功能正常
- ✅ **展开/折叠增强**：多层级文件夹展开/折叠功能正常
- ✅ **状态持久化**：展开状态在页面刷新后正确恢复
- ✅ **实时同步**：数据库操作后自动刷新界面

### 测试覆盖的场景
1. 页面加载和认证流程
2. 目录树数据加载和显示
3. **文件编辑功能测试**：
   - 双击Markdown文件打开编辑器
   - 双击HTML文件打开编辑器
   - 多标签页管理和切换
   - 编辑器语法高亮和工具栏功能
   - 编辑/预览模式切换
   - 快捷键保存功能（Ctrl+S）
   - 未保存更改状态检测
   - 云端保存和数据同步
4. **右键菜单上下文测试**：
   - 空白区域右键创建根目录文件/文件夹
   - 文件夹右键创建子文件/文件夹
   - 菜单位置和选项正确性验证
5. **多层级展开/折叠测试**：
   - 单层文件夹展开/折叠功能
   - 多层级嵌套文件夹展开/折叠
   - 展开状态持久化（页面刷新后恢复）
6. 重命名文件夹并验证更新
7. 软删除文件夹到回收站并验证移除
8. 回收站展开/折叠功能测试
9. 从回收站恢复文件并验证恢复
10. 永久删除文件并验证彻底移除
11. 标签页切换和数据过滤
12. 错误处理和加载状态

## 📊 数据模型

### DirectoryItem 数据结构
```typescript
interface DirectoryItem {
  _id: string              // 唯一标识
  name: string             // 文件/文件夹名称
  type: 'folder' | 'note' | 'html'  // 类型
  parentId: string | null  // 父级ID
  path: string             // 完整路径
  level: number            // 层级深度
  children?: DirectoryItem[] // 子项目（前端构建）
  content?: string         // 文件内容
  isShared?: boolean       // 是否分享
  importance?: number      // 重要性等级
  isExpanded?: boolean     // 是否展开（文件夹）
  isDeleted?: boolean      // 是否已删除（回收站标记）
  deletedAt?: string       // 删除时间（ISO字符串）
  createdAt: string        // 创建时间
  updatedAt: string        // 更新时间
  createdBy: string        // 创建者
}
```

## 🔧 配置说明

### 云开发配置
项目使用 `@cloudbase/js-sdk` 连接云开发环境：

```typescript
const app = cloudbase.init({
  env: 'ai-demo-8gjoyg63e237ce06'
})
```

### 数据库权限
- 前端使用匿名登录访问云开发
- 云函数具有完整的数据库读写权限
- 建议在生产环境中配置适当的安全规则

## 🛠️ 本地开发

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建项目
```bash
npm run build
```

### 部署到云开发

#### 自动化部署（推荐）
```bash
# 完整部署流程（构建 + 清理 + 部署）
npm run deploy

# 仅部署（不重新构建）
npm run deploy:clean

# 完整流程（包含代码检查）
npm run deploy:full
```

#### 手动部署
```bash
# 使用 CloudBase CLI
npx @cloudbase/cli hosting deploy dist -e ai-demo-8gjoyg63e237ce06

# 或使用框架部署
npx @cloudbase/cli framework deploy
```

#### 部署特性
- ✅ **自动清理**：部署前自动删除旧版本文件
- ✅ **单版本策略**：只保留最新版本，节省存储空间
- ✅ **文档同步**：自动更新README中的访问地址
- ✅ **零停机**：部署过程不影响服务可用性

详细部署指南请参考：[部署文档](docs/DEPLOYMENT.md)

## 📝 更新日志

### v3.2.0 (2025-01-21) - 文件编辑功能修复版
- 📝 **文件编辑功能完全修复**：解决了Markdown和HTML文件无法正常编辑的问题
- ✨ **核心问题修复**：
  - ✅ 文件内容加载：双击目录树中的文件时，内容正确在编辑器中显示
  - ✅ 编辑器显示：Monaco编辑器正确渲染文件内容，支持语法高亮
  - ✅ 文件类型支持：Markdown和HTML文件的编辑功能完全正常
  - ✅ 错误处理：系统能够正确处理文件状态和错误情况
- 🛠️ **技术架构修复**：
  - 修复DirectoryTree和NotesStore之间的数据同步问题
  - 新增 `openDirectoryItemInTab` 方法支持从真实文件数据创建标签页
  - 新增 `getItem` 云函数方法获取单个文件详细信息
  - 新增 `saveTabToCloud` 方法支持保存到云数据库
  - 修复action名称不匹配和数据查询问题
- 🎨 **用户体验提升**：
  - 多标签页系统：支持同时编辑多个文件
  - 实时状态跟踪：正确显示"有未保存的更改"状态
  - 快捷键支持：Ctrl+S保存、Ctrl+B加粗、Ctrl+I斜体等
  - 编辑/预览切换：Markdown和HTML文件的无缝预览
  - 通知系统：保存成功/失败的即时反馈
- ✅ **全面测试验证**：通过Playwright端到端测试验证所有编辑功能

### v3.1.0 (2025-01-21) - 右键菜单和展开/折叠增强版
- 🖱️ **右键菜单上下文优化**：智能识别右键目标，提供精确的上下文操作
- ✨ **核心功能实现**：
  - ✅ 空白区域右键：在根目录下创建新文件/文件夹
  - ✅ 文件夹右键：在该文件夹内创建子文件/文件夹
  - ✅ 上下文感知：根据右键位置动态生成菜单选项
  - ✅ 多层级支持：支持无限层级的文件夹嵌套
- 📁 **展开/折叠功能增强**：完整的文件夹展开/折叠交互体验
- ✨ **核心功能实现**：
  - ✅ 展开/折叠切换：点击文件夹图标或名称切换展开状态
  - ✅ 视觉指示器：清晰的箭头图标显示展开/折叠状态
  - ✅ 状态持久化：刷新页面后记住所有文件夹的展开状态
  - ✅ 多层级独立：每个层级的展开/折叠状态独立管理
  - ✅ 自动展开：创建子项目后父文件夹自动展开显示
- 🛠️ **技术架构优化**：
  - 扩展 `DirectoryTree` 组件支持上下文右键菜单
  - 增强 `TreeItem` 组件的展开/折叠交互逻辑
  - 优化 `directoryStore` 状态管理和localStorage持久化
  - 改进云函数 `createItem` 支持指定parentId参数
- 🎨 **UI/UX提升**：
  - 右键菜单位置智能调整，避免超出屏幕边界
  - 展开/折叠动画流畅自然，提升交互体验
  - 新建项目后自动刷新并显示，提供即时反馈
  - 深色太空主题完美适配所有新功能
- ✅ **全面测试验证**：通过Playwright端到端测试验证所有增强功能

### v3.0.0 (2025-01-21) - 回收站功能完整版
- 🗑️ **新增回收站功能**：完整的回收站系统，支持软删除和数据恢复
- ✨ **核心功能实现**：
  - ✅ 软删除：删除操作改为移到回收站，可恢复
  - ✅ 回收站UI：左侧目录树底部显示回收站入口和文件数量
  - ✅ 展开/折叠：支持展开查看回收站内容
  - ✅ 恢复功能：支持将文件/文件夹恢复到原位置
  - ✅ 永久删除：提供彻底删除选项（不可恢复）
  - ✅ 清空回收站：批量永久删除所有回收站内容
  - ✅ 删除信息显示：显示删除时间和原路径信息
- 🔧 **数据模型扩展**：
  - 添加 `isDeleted` 字段标记删除状态
  - 添加 `deletedAt` 时间戳记录删除时间
  - 保留原有的 `parentId` 和 `path` 信息用于恢复
- 🛠️ **云函数增强**：
  - 修改 `deleteItem` 实现软删除
  - 新增 `restoreItem` 云函数用于恢复文件
  - 新增 `permanentDelete` 云函数用于永久删除
  - 新增 `getTrashItems` 云函数获取回收站内容
  - 新增 `emptyTrash` 云函数清空回收站
- 🎨 **UI/UX优化**：
  - 深色太空主题适配的回收站界面
  - 确认对话框文本更新为"移到回收站"
  - 回收站项目显示删除时间和原路径
  - 支持恢复和永久删除的右键操作
- ✅ **全面测试验证**：通过Playwright端到端测试验证所有回收站功能

### v2.1.0 (2025-01-21) - 认证问题修复版
- 🔧 **修复认证问题**：解决了CloudBase SDK匿名登录认证错误
- ✅ **全面功能测试**：通过Playwright自动化测试验证所有CRUD操作
- 🎯 **功能验证完成**：
  - ✅ 目录树正常加载和显示
  - ✅ 三标签页（全部、最近、分享）切换正常
  - ✅ 右键菜单创建文件/文件夹功能正常
  - ✅ 文件重命名功能正常
  - ✅ 文件删除功能正常（含确认对话框）
  - ✅ 展开/折叠文件夹功能正常
  - ✅ 实时数据同步正常
- 🚀 **性能优化**：改进认证流程和错误处理机制

### v2.0.0 (2025-01-21)
- ✨ 实现完整的目录树CRUD功能
- ✨ 集成云数据库存储
- ✨ 添加云函数后端支持
- ✨ 优化三标签视图体验
- ✨ 完善错误处理和加载状态
- ✨ 添加自动化测试

### v1.0.0
- 🎉 初始版本发布
- 基础的笔记编辑功能
- 简单的文件管理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

感谢腾讯云开发团队提供的优秀云服务平台。
