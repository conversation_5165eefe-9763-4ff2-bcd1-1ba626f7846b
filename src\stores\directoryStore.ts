import { create } from 'zustand'
import { directoryService, DirectoryItem } from '../services/directoryService'

interface DirectoryState {
  // 状态
  items: DirectoryItem[]
  loading: boolean
  error: string | null
  expandedNodes: Set<string>
  selectedItemId: string | null

  // 计算属性
  recentFiles: DirectoryItem[]
  sharedFiles: DirectoryItem[]
  allFiles: DirectoryItem[]
  trashItems: DirectoryItem[]

  // 操作方法
  loadDirectoryTree: () => Promise<void>
  createItem: (data: {
    name: string
    type: 'folder' | 'note' | 'html'
    parentId?: string
    content?: string
  }) => Promise<DirectoryItem>
  updateItem: (id: string, updates: Partial<DirectoryItem>) => Promise<void>
  deleteItem: (id: string) => Promise<void>
  moveItem: (id: string, newParentId: string) => Promise<void>

  // 局部更新方法
  addItemToTree: (newItem: DirectoryItem) => void
  updateItemInTree: (id: string, updates: Partial<DirectoryItem>) => void
  removeItemFromTree: (id: string) => void
  moveItemInTree: (id: string, newParentId: string) => void

  // 回收站操作
  loadTrashItems: () => Promise<void>
  restoreItem: (id: string) => Promise<void>
  permanentDeleteItem: (id: string) => Promise<void>
  emptyTrash: () => Promise<void>

  // UI 操作
  toggleExpanded: (id: string) => void
  setSelectedItem: (id: string | null) => void
  setError: (error: string | null) => void

  // 展开状态持久化
  saveExpandedState: () => void
  loadExpandedState: () => void

  // 数据刷新
  refreshRecentFiles: () => Promise<void>
  refreshSharedFiles: () => Promise<void>
  refreshTrashItems: () => Promise<void>
}

// 从localStorage加载展开状态
const loadExpandedNodesFromStorage = (): Set<string> => {
  try {
    const saved = localStorage.getItem('directoryTree_expandedNodes')
    if (saved) {
      return new Set(JSON.parse(saved))
    }
  } catch (error) {
    console.warn('Failed to load expanded nodes from storage:', error)
  }
  return new Set(['root'])
}

export const useDirectoryStore = create<DirectoryState>((set, get) => ({
  // 初始状态
  items: [],
  loading: false,
  error: null,
  expandedNodes: loadExpandedNodesFromStorage(),
  selectedItemId: null,
  recentFiles: [],
  sharedFiles: [],
  allFiles: [],
  trashItems: [],
  
  // 加载目录树
  loadDirectoryTree: async () => {
    set({ loading: true, error: null })
    
    try {
      const items = await directoryService.getDirectoryTree()
      
      // 提取所有文件（非文件夹）
      const allFiles = extractAllFiles(items)
      
      set({ 
        items, 
        allFiles,
        loading: false 
      })
      
      // 同时刷新最近文件、分享文件和回收站
      get().refreshRecentFiles()
      get().refreshSharedFiles()
      get().refreshTrashItems()
      
    } catch (error) {
      console.error('Failed to load directory tree:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to load directory tree',
        loading: false 
      })
    }
  },
  
  // 创建项目
  createItem: async (data) => {
    set({ loading: true, error: null })

    try {
      const newItem = await directoryService.createItem(data)

      // 使用局部更新而不是重新加载整个树
      get().addItemToTree(newItem)

      // 更新相关的计算属性
      const allFiles = extractAllFiles(get().items)
      set({ allFiles, loading: false })

      // 刷新相关列表
      get().refreshRecentFiles()
      get().refreshSharedFiles()

      return newItem

    } catch (error) {
      console.error('Failed to create item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to create item',
        loading: false
      })
      throw error
    }
  },
  
  // 更新项目
  updateItem: async (id, updates) => {
    set({ loading: true, error: null })

    try {
      await directoryService.updateItem(id, updates)

      // 使用局部更新而不是重新加载整个树
      get().updateItemInTree(id, updates)

      // 更新相关的计算属性
      const allFiles = extractAllFiles(get().items)
      set({ allFiles, loading: false })

      // 刷新相关列表
      get().refreshRecentFiles()
      get().refreshSharedFiles()

    } catch (error) {
      console.error('Failed to update item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update item',
        loading: false
      })
      throw error
    }
  },
  
  // 删除项目
  deleteItem: async (id) => {
    set({ loading: true, error: null })

    try {
      await directoryService.deleteItem(id)

      // 使用局部更新而不是重新加载整个树
      get().removeItemFromTree(id)

      // 如果删除的是当前选中项，清除选中状态
      if (get().selectedItemId === id) {
        set({ selectedItemId: null })
      }

      // 更新相关的计算属性
      const allFiles = extractAllFiles(get().items)
      set({ allFiles, loading: false })

      // 刷新相关列表
      get().refreshRecentFiles()
      get().refreshSharedFiles()
      get().refreshTrashItems()

    } catch (error) {
      console.error('Failed to delete item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete item',
        loading: false
      })
      throw error
    }
  },
  
  // 移动项目
  moveItem: async (id, newParentId) => {
    set({ loading: true, error: null })

    try {
      await directoryService.moveItem(id, newParentId)

      // 使用局部更新而不是重新加载整个树
      get().moveItemInTree(id, newParentId)

      // 更新相关的计算属性
      const allFiles = extractAllFiles(get().items)
      set({ allFiles, loading: false })

      // 刷新相关列表
      get().refreshRecentFiles()
      get().refreshSharedFiles()

    } catch (error) {
      console.error('Failed to move item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to move item',
        loading: false
      })
      throw error
    }
  },
  
  // 切换展开状态
  toggleExpanded: (id) => {
    const expandedNodes = new Set(get().expandedNodes)
    if (expandedNodes.has(id)) {
      expandedNodes.delete(id)
    } else {
      expandedNodes.add(id)
    }
    set({ expandedNodes })

    // 保存到localStorage
    get().saveExpandedState()
  },
  
  // 设置选中项
  setSelectedItem: (id) => {
    set({ selectedItemId: id })
  },
  
  // 设置错误
  setError: (error) => {
    set({ error })
  },
  
  // 刷新最近文件
  refreshRecentFiles: async () => {
    try {
      const recentFiles = await directoryService.getRecentFiles()
      set({ recentFiles })
    } catch (error) {
      console.error('Failed to refresh recent files:', error)
    }
  },
  
  // 刷新分享文件
  refreshSharedFiles: async () => {
    try {
      const sharedFiles = await directoryService.getSharedFiles()
      set({ sharedFiles })
    } catch (error) {
      console.error('Failed to refresh shared files:', error)
    }
  },

  // 刷新回收站
  refreshTrashItems: async () => {
    try {
      const trashItems = await directoryService.getTrashItems()
      set({ trashItems })
    } catch (error) {
      console.error('Failed to refresh trash items:', error)
    }
  },

  // 加载回收站数据（用于弹窗）
  loadTrashItems: async () => {
    set({ loading: true, error: null })
    try {
      const trashItems = await directoryService.getTrashItems()
      set({ trashItems, loading: false })
    } catch (error) {
      console.error('Failed to load trash items:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to load trash items',
        loading: false
      })
    }
  },

  // 恢复项目
  restoreItem: async (id) => {
    set({ loading: true, error: null })

    try {
      await directoryService.restoreItem(id)

      // 重新加载目录树和回收站
      await get().loadDirectoryTree()

      set({ loading: false })

    } catch (error) {
      console.error('Failed to restore item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to restore item',
        loading: false
      })
      throw error
    }
  },

  // 永久删除项目
  permanentDeleteItem: async (id) => {
    set({ loading: true, error: null })

    try {
      await directoryService.permanentDelete(id)

      // 重新加载回收站
      await get().refreshTrashItems()

      set({ loading: false })

    } catch (error) {
      console.error('Failed to permanently delete item:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to permanently delete item',
        loading: false
      })
      throw error
    }
  },

  // 清空回收站
  emptyTrash: async () => {
    set({ loading: true, error: null })

    try {
      await directoryService.emptyTrash()

      // 重新加载回收站
      await get().refreshTrashItems()

      set({ loading: false })

    } catch (error) {
      console.error('Failed to empty trash:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to empty trash',
        loading: false
      })
      throw error
    }
  },

  // 保存展开状态到localStorage
  saveExpandedState: () => {
    try {
      const expandedNodes = Array.from(get().expandedNodes)
      localStorage.setItem('directoryTree_expandedNodes', JSON.stringify(expandedNodes))
    } catch (error) {
      console.warn('Failed to save expanded nodes to storage:', error)
    }
  },

  // 从localStorage加载展开状态
  loadExpandedState: () => {
    try {
      const saved = localStorage.getItem('directoryTree_expandedNodes')
      if (saved) {
        const expandedNodes = new Set<string>(JSON.parse(saved))
        set({ expandedNodes })
      }
    } catch (error) {
      console.warn('Failed to load expanded nodes from storage:', error)
    }
  },

  // 局部更新方法：添加新项目到树中
  addItemToTree: (newItem: DirectoryItem) => {
    const items = [...get().items]
    const updatedItems = addItemToTreeRecursive(items, newItem)
    set({ items: updatedItems })
  },

  // 局部更新方法：更新树中的项目
  updateItemInTree: (id: string, updates: Partial<DirectoryItem>) => {
    const items = [...get().items]
    const updatedItems = updateItemInTreeRecursive(items, id, updates)
    set({ items: updatedItems })
  },

  // 局部更新方法：从树中移除项目
  removeItemFromTree: (id: string) => {
    const items = [...get().items]
    const updatedItems = removeItemFromTreeRecursive(items, id)
    set({ items: updatedItems })
  },

  // 局部更新方法：在树中移动项目
  moveItemInTree: (id: string, newParentId: string) => {
    const items = [...get().items]
    const updatedItems = moveItemInTreeRecursive(items, id, newParentId)
    set({ items: updatedItems })
  }
}))

/**
 * 从目录树中提取所有文件
 */
function extractAllFiles(items: DirectoryItem[]): DirectoryItem[] {
  const files: DirectoryItem[] = []

  function traverse(nodes: DirectoryItem[]) {
    for (const node of nodes) {
      if (node.type !== 'folder') {
        files.push(node)
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }

  traverse(items)
  return files
}

/**
 * 递归添加新项目到树中
 */
function addItemToTreeRecursive(items: DirectoryItem[], newItem: DirectoryItem): DirectoryItem[] {
  const parentId = newItem.parentId || 'root'

  // 如果是根级项目
  if (parentId === 'root' || !parentId) {
    return [...items, { ...newItem, children: [] }]
  }

  // 递归查找父级并添加
  return items.map(item => {
    if (item._id === parentId) {
      const children = item.children || []
      return {
        ...item,
        children: [...children, { ...newItem, children: [] }]
      }
    } else if (item.children && item.children.length > 0) {
      return {
        ...item,
        children: addItemToTreeRecursive(item.children, newItem)
      }
    }
    return item
  })
}

/**
 * 递归更新树中的项目
 */
function updateItemInTreeRecursive(items: DirectoryItem[], id: string, updates: Partial<DirectoryItem>): DirectoryItem[] {
  return items.map(item => {
    if (item._id === id) {
      return { ...item, ...updates }
    } else if (item.children && item.children.length > 0) {
      return {
        ...item,
        children: updateItemInTreeRecursive(item.children, id, updates)
      }
    }
    return item
  })
}

/**
 * 递归从树中移除项目
 */
function removeItemFromTreeRecursive(items: DirectoryItem[], id: string): DirectoryItem[] {
  return items
    .filter(item => item._id !== id)
    .map(item => {
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: removeItemFromTreeRecursive(item.children, id)
        }
      }
      return item
    })
}

/**
 * 递归在树中移动项目
 */
function moveItemInTreeRecursive(items: DirectoryItem[], id: string, newParentId: string): DirectoryItem[] {
  // 首先找到要移动的项目
  let itemToMove: DirectoryItem | null = null

  function findItem(nodes: DirectoryItem[]): DirectoryItem | null {
    for (const node of nodes) {
      if (node._id === id) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findItem(node.children)
        if (found) return found
      }
    }
    return null
  }

  itemToMove = findItem(items)
  if (!itemToMove) return items

  // 从原位置移除
  const itemsWithoutMoved = removeItemFromTreeRecursive(items, id)

  // 添加到新位置
  const movedItem = { ...itemToMove, parentId: newParentId || 'root' }
  return addItemToTreeRecursive(itemsWithoutMoved, movedItem)
}
