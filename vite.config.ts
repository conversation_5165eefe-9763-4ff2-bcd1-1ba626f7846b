import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['framer-motion', 'lucide-react'],
          editor: ['monaco-editor', '@monaco-editor/react'],
          three: ['three', '@react-three/fiber', '@react-three/drei']
        }
      }
    }
  },
  base: './',
  server: {
    port: 3000,
    open: true
  }
})
