/* 图标样式 - 深色主题优化 */

/* 基础图标样式 */
.icon-base {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
  transition: all 0.2s ease-out;
}

/* 图标尺寸 */
.icon-xs {
  width: 12px;
  height: 12px;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-md {
  width: 20px;
  height: 20px;
}

.icon-lg {
  width: 24px;
  height: 24px;
}

.icon-xl {
  width: 32px;
  height: 32px;
}

/* 图标颜色变体 - 深色主题优化 */
.icon-primary {
  color: var(--theme-primary);
}

.icon-secondary {
  color: var(--theme-text-muted);
}

.icon-muted {
  color: var(--theme-text-subtle);
}

.icon-success {
  color: #22c55e;
}

.icon-warning {
  color: #f59e0b;
}

.icon-error {
  color: #ef4444;
}

.icon-info {
  color: var(--theme-primary);
}

/* 深色主题下的图标颜色调整 */
[data-theme="dark"] .icon-success {
  color: #4ade80;
}

[data-theme="dark"] .icon-warning {
  color: #fbbf24;
}

[data-theme="dark"] .icon-error {
  color: #f87171;
}

/* 交互状态 */
.icon-interactive {
  cursor: pointer;
  transition: all 0.15s ease-out;
}

.icon-interactive:hover {
  color: var(--theme-primary);
  transform: scale(1.05);
}

.icon-interactive:active {
  transform: scale(0.95);
}

/* 按钮中的图标 */
.btn-primary .icon-base,
.btn-primary [class*="icon-"] {
  color: white;
}

.btn-secondary .icon-base,
.btn-secondary [class*="icon-"] {
  color: var(--theme-text);
}

.btn-ghost .icon-base,
.btn-ghost [class*="icon-"] {
  color: var(--theme-text-muted);
}

.btn-ghost:hover .icon-base,
.btn-ghost:hover [class*="icon-"] {
  color: var(--theme-text);
}

/* 导航图标 */
.nav-icon {
  @apply icon-base icon-lg;
  color: var(--theme-text-muted);
  transition: color 0.2s ease-out;
}

.nav-icon:hover,
.nav-icon.active {
  color: var(--theme-primary);
}

/* 文件类型图标 */
.file-icon {
  @apply icon-base icon-md;
  color: var(--theme-primary);
  opacity: 0.8;
}

.folder-icon {
  @apply icon-base icon-md;
  color: var(--theme-primary);
  opacity: 0.9;
}

.folder-icon.open {
  color: var(--theme-primary);
  opacity: 1;
}

/* 状态图标 */
.status-icon {
  @apply icon-base icon-sm;
}

.status-icon.loading {
  animation: spin 1s linear infinite;
}

.status-icon.success {
  color: #22c55e;
}

.status-icon.error {
  color: #ef4444;
}

.status-icon.warning {
  color: #f59e0b;
}

/* 深色主题下的状态图标 */
[data-theme="dark"] .status-icon.success {
  color: #4ade80;
}

[data-theme="dark"] .status-icon.error {
  color: #f87171;
}

[data-theme="dark"] .status-icon.warning {
  color: #fbbf24;
}

/* 主题切换图标特殊样式 */
.theme-toggle-icon {
  @apply icon-base icon-lg;
  transition: all 0.3s ease-out;
}

.theme-toggle-icon:hover {
  transform: rotate(15deg) scale(1.1);
}

/* 搜索图标 */
.search-icon {
  @apply icon-base icon-md;
  color: var(--theme-text-muted);
  transition: color 0.2s ease-out;
}

.search-icon:hover,
.search-icon.active {
  color: var(--theme-primary);
}

/* 操作图标 */
.action-icon {
  @apply icon-base icon-md icon-interactive;
  color: var(--theme-text-muted);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.15s ease-out;
}

.action-icon:hover {
  color: var(--theme-text);
  background-color: var(--theme-surface-hover);
}

.action-icon.danger:hover {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

[data-theme="dark"] .action-icon.danger:hover {
  color: #f87171;
  background-color: rgba(248, 113, 113, 0.1);
}

/* 图标动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 图标组合 */
.icon-with-text {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.icon-with-text .icon-base {
  flex-shrink: 0;
}

/* 图标徽章 */
.icon-badge {
  position: relative;
}

.icon-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2px solid var(--theme-bg);
}

[data-theme="dark"] .icon-badge::after {
  background-color: #f87171;
}

/* 图标分组 */
.icon-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.icon-group .icon-base {
  opacity: 0.7;
  transition: opacity 0.2s ease-out;
}

.icon-group:hover .icon-base {
  opacity: 1;
}

/* 响应式图标 */
@media (max-width: 640px) {
  .icon-responsive {
    width: 20px;
    height: 20px;
  }
}

@media (min-width: 641px) {
  .icon-responsive {
    width: 24px;
    height: 24px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .icon-base {
    stroke-width: 2.5;
  }
  
  .icon-muted {
    color: var(--theme-text);
    opacity: 0.8;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .icon-base,
  .icon-interactive,
  .theme-toggle-icon,
  .action-icon {
    transition: none;
  }
  
  .status-icon.loading {
    animation: none;
  }
}
